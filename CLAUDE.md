# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (Maven)
- **Development**: `mvn spring-boot:run` (from `ruoyi-admin` module)
- **Build**: `mvn clean package -Dmaven.test.skip=true`
- **Test**: `mvn test`
- **Install dependencies**: `mvn clean install`

### Frontend (Vue.js)
- **Development**: `npm run dev` (from `ruoyi-ui` directory)
- **Build**: `npm run build:prod`
- **Lint**: `npm run lint`

### Application Access
- **Backend**: http://localhost:8081 (default port)
- **Frontend**: http://localhost:80 (development server)

## Architecture Overview

This is a **RuoYi-Flowable-Plus** system - a Spring Boot + Vue.js administrative platform with integrated Flowable workflow engine.

### Technology Stack
- **Backend**: Spring Boot 2.7.11, Java 11, My<PERSON>atis-Plus, Flowable 6.8.0
- **Frontend**: Vue.js 2.6.12, Element-UI 2.15.12, Axios
- **Database**: MySQL (primary), supports Oracle/PostgreSQL/SQL Server
- **Authentication**: Sa-Token
- **Cache**: Redis (via Redisson)
- **Build Tools**: Maven (backend), Vue CLI (frontend)

### Module Structure
```
ruoyi-admin/          # Main application module (Spring Boot entry point)
ruoyi-common/         # Common utilities and shared code
ruoyi-system/         # System management (users, roles, menus, etc.)
ruoyi-flowable/       # Flowable workflow engine integration
ruoyi-framework/      # Framework configuration and security
ruoyi-generator/      # Code generation module
ruoyi-job/           # Scheduled tasks (XXL-Job)
ruoyi-oss/           # Object storage service
ruoyi-sms/           # SMS service integration
ruoyi-demo/          # Demo/example modules
ruoyi-extend/        # Extended modules (monitoring, job admin)
ruoyi-ui/            # Vue.js frontend application
```

### Key Features
- **Workflow Management**: Flowable BPMN process engine with visual designer
- **Form Designer**: Online form creation and management
- **Task Management**: Workflow task handling and tracking
- **Multi-tenant Support**: Configurable tenant isolation
- **Code Generation**: Automatic CRUD code generation
- **System Monitoring**: Built-in monitoring and logging

### Database Configuration
- Default profile: `dev` (development)
- Configuration files: `application.yml`, `application-dev.yml`, `application-prod.yml`
- Database schema auto-updates enabled for Flowable
- MyBatis-Plus with automatic camelCase mapping

### Development Notes
- Main application class: `com.ruoyi.RuoYiApplication` (in ruoyi-admin)
- Default server port: 8081
- File upload path: `/opt/flow/ruoyi/uploadPath`
- Flowable IDM engine disabled (uses custom user management)
- Node.js legacy OpenSSL provider required for frontend build

### Common File Locations
- **Backend configs**: `ruoyi-admin/src/main/resources/`
- **Frontend source**: `ruoyi-ui/src/`
- **Database scripts**: `script/sql/mysql/`
- **MyBatis mappers**: `src/main/resources/mapper/`
- **Workflow definitions**: Managed through Flowable designer

### Testing
- Unit tests configured with Maven Surefire plugin
- Test profiles: `dev`, `prod`, `local`
- Excluded test groups: `exclude`