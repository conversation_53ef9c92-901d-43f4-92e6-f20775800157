# FormHistoryTaskListener 表单历史记录监听器使用说明

## 概述

FormHistoryTaskListener 是一个专门用于保存表单历史记录的 Flowable 任务监听器。它将原先在撤回时保存表单历史记录的功能移动到了任务触发器中，使得表单历史记录的保存更加灵活和可配置。

## 功能特性

### 1. 自动表单历史保存
- 在任务创建时自动保存表单数据作为历史记录
- 支持全局变量和本地变量的合并保存
- 自动计算撤回次数

### 2. 灵活的配置选项
- **operationType**: 操作类型（可配置：TRIGGER、RETURN、REVOKE等）
- **remark**: 备注信息（可自定义保存原因）

### 3. 完善的错误处理
- 异常不会影响正常流程执行
- 详细的日志记录
- 自动跳过无表单数据的任务

## 在 Flowable 流程中的配置

### 1. 基本配置

在流程设计器中为需要保存表单历史的任务添加监听器：

```xml
<userTask id="approvalTask" name="审批任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.FormHistoryTaskListener">
      <flowable:field name="operationType">
        <flowable:string>TRIGGER</flowable:string>
      </flowable:field>
      <flowable:field name="remark">
        <flowable:string>任务创建时自动保存表单历史</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 2. 退回场景配置

为退回相关的任务配置特定的操作类型：

```xml
<userTask id="returnTask" name="退回处理">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.FormHistoryTaskListener">
      <flowable:field name="operationType">
        <flowable:string>RETURN</flowable:string>
      </flowable:field>
      <flowable:field name="remark">
        <flowable:string>退回前表单数据备份</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 3. 撤回场景配置

为撤回相关的任务配置：

```xml
<userTask id="revokeTask" name="撤回处理">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.FormHistoryTaskListener">
      <flowable:field name="operationType">
        <flowable:string>REVOKE</flowable:string>
      </flowable:field>
      <flowable:field name="remark">
        <flowable:string>撤回前表单数据备份</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 4. 动态配置

使用表达式进行动态配置：

```xml
<userTask id="dynamicTask" name="动态任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.FormHistoryTaskListener">
      <flowable:field name="operationType">
        <flowable:expression>${taskType == 'return' ? 'RETURN' : 'TRIGGER'}</flowable:expression>
      </flowable:field>
      <flowable:field name="remark">
        <flowable:expression>任务${taskName}的表单历史保存</flowable:expression>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

## 配置参数说明

### operationType（操作类型）
- **TRIGGER**: 触发器保存（默认值）
- **RETURN**: 退回操作
- **REVOKE**: 撤回操作
- **CUSTOM**: 自定义操作类型

### remark（备注信息）
- 用于记录保存表单历史的原因
- 默认值：`任务触发器自动保存表单历史数据`
- 支持表达式动态生成

## 数据库表结构

表单历史数据保存在 `wf_form_history` 表中：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | varchar(64) | 主键ID |
| process_instance_id | varchar(64) | 流程实例ID |
| task_id | varchar(64) | 任务ID |
| task_name | varchar(255) | 任务名称 |
| operation_type | varchar(20) | 操作类型 |
| revoke_count | int(11) | 撤回次数 |
| form_data | longtext | 表单数据（JSON格式） |
| operator_id | varchar(64) | 操作用户ID |
| operator_name | varchar(100) | 操作用户名称 |
| operation_time | datetime | 操作时间 |
| remark | varchar(500) | 备注 |

## 迁移说明

### 从旧版本迁移

如果您之前使用的是 WfTaskServiceImpl 中的表单历史保存功能，现在需要：

1. **移除旧代码依赖**: 旧的 `saveFormDataBeforeReturn` 等方法已标记为 `@Deprecated`
2. **配置新监听器**: 在需要保存表单历史的任务上配置 FormHistoryTaskListener
3. **测试验证**: 确保新的监听器能正确保存表单历史数据

### 兼容性说明

- 新的监听器与现有的表单历史数据表完全兼容
- 数据格式保持一致，无需修改查询逻辑
- 可以与其他任务监听器同时使用

## 最佳实践

### 1. 选择合适的触发事件
- 推荐使用 `create` 事件，在任务创建时保存表单历史
- 避免在 `complete` 事件中保存，可能导致数据不一致

### 2. 合理配置操作类型
- 根据业务场景选择合适的 operationType
- 使用有意义的备注信息便于后续查询

### 3. 性能考虑
- 监听器会在每个配置的任务创建时执行
- 对于大量任务的流程，考虑性能影响
- 可以通过条件表达式控制是否执行

### 4. 错误处理
- 监听器异常不会影响流程执行
- 关注日志中的错误信息
- 定期检查表单历史数据的完整性

## 示例流程配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn">
  
  <process id="sampleProcess" name="示例流程">
    
    <!-- 开始节点 -->
    <startEvent id="start"/>
    
    <!-- 申请任务 - 保存初始表单数据 -->
    <userTask id="applyTask" name="提交申请">
      <extensionElements>
        <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.FormHistoryTaskListener">
          <flowable:field name="operationType">
            <flowable:string>TRIGGER</flowable:string>
          </flowable:field>
          <flowable:field name="remark">
            <flowable:string>申请提交时保存表单数据</flowable:string>
          </flowable:field>
        </flowable:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 审批任务 - 保存审批前表单数据 -->
    <userTask id="approveTask" name="审批">
      <extensionElements>
        <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.FormHistoryTaskListener">
          <flowable:field name="operationType">
            <flowable:string>TRIGGER</flowable:string>
          </flowable:field>
          <flowable:field name="remark">
            <flowable:string>审批前保存表单数据</flowable:string>
          </flowable:field>
        </flowable:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 结束节点 -->
    <endEvent id="end"/>
    
    <!-- 流程连线 -->
    <sequenceFlow id="flow1" sourceRef="start" targetRef="applyTask"/>
    <sequenceFlow id="flow2" sourceRef="applyTask" targetRef="approveTask"/>
    <sequenceFlow id="flow3" sourceRef="approveTask" targetRef="end"/>
    
  </process>
</definitions>
```

## 总结

FormHistoryTaskListener 提供了一个灵活、可配置的方式来保存表单历史记录。通过将功能从业务代码移动到任务监听器中，实现了更好的关注点分离和可维护性。用户可以根据具体的业务需求灵活配置监听器，实现精确的表单历史记录管理。
