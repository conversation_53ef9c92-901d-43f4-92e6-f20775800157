import dayjs from "dayjs";
import { getCalendarInfo } from "@/api/workCalendar";

export default {
  name: "CalendarInfoMixin",
  data() {
    return {
      activeDay: dayjs(),
      // Mock data
      infoData: {
        todoList: [], // 值班信息 {date,userCode,userName}
        memo: [], // 行政信息 {id date name}
        arrange: [], // 交易所信息 {date,name,symbol}
        handoverMatters: [], // string []
        specialMatters: [], // string []
      },
    };
  },
  provide() {
    return {
      getCurrentDay: () => this.shareCurrentDay,
      changeCurrentDate: this.shareChangeCurrentDate,
      getInfoData: () => this.infoData,
      updateInfoData: this.getCalendarInfoData,
    };
  },
  created() {
    this.getCalendarInfoData();
  },
  methods: {
    shareChangeCurrentDate(date) {
      this.activeDay = dayjs(date);
      this.getCalendarInfoData();
    },
    getCalendarInfoData() {
      getCalendarInfo(this.shareCurrentDay).then((res) => {
        if (res.code === 200) {
          // let handoverMatters = [];
          // let specialMatters = [];

          // if (res.result.dutyLog.handoverMatters) {
          //   handoverMatters = res.result.dutyLog.handoverMatters.split("\n");
          // }
          // if (res.result.dutyLog.specialMatters != null) {
          //   specialMatters = res.result.dutyLog.specialMatters.split("\n");
          // }

          this.infoData = {
            arrange: (res.data.arrange ? res.data.arrange : []).map((item) => ({
              ...item,
              label: item.shiftName,
              // date:
              type: "arrange",
            })),
            todoList: (res.data.todoList ? res.data.todoList : []).map(
              (item) => ({
                ...item,
                label: `${item.taskName}`,
                date: `${item.taskStartTime} - ${item.taskEndTime}`,
                type: "todoList",
              })
            ),
            // memo: (res.result.memo ? res.result.memo : []).map((item) => ({
            //   ...item,
            //   label: item.name,
            //   type: "memo",
            // })),
            // handoverMatters,
            // specialMatters,
          };
        }
      });
    },
  },
  computed: {
    shareCurrentDay() {
      return this.activeDay.format("YYYY-MM-DD");
    },
  },
};
