import dayjs from "dayjs";

const weekDays = ['日', '一', '二', '三', '四', '五', '六'];

export function generateCalendarWithRestDays(date) {
  const inputDate = dayjs(date);
  const today = dayjs();

  const currentDay = today.date();
  const currentMonth = today.month();
  const currentYear = today.year();

  const year = inputDate.year();
  const month = inputDate.month();

  const firstDayOfMonth = inputDate.startOf('month').day();
  const daysInMonth = inputDate.daysInMonth();
  const prevMonth = inputDate.subtract(1, 'month');
  const prevMonthDays = prevMonth.daysInMonth();

  const calendar = [];

  let day = 1;
  let nextMonthDay = 1;

  for (let i = 0; i < 6; i++) {
    const week = [];
    for (let j = 0; j < 7; j++) {
      if (i === 0 && j < firstDayOfMonth) {
        // Previous month
        const prevMonthDate = prevMonthDays - firstDayOfMonth + j + 1;
        const fullDate = prevMonth.year() + '-' + (prevMonth.month() + 1).toString().padStart(2, '0') + '-' + prevMonthDate.toString().padStart(2, '0');
        week.push({
          day: prevMonthDate,
          fullDate,
          isRestDay: j === 0 || j === 6,
          isToday: false,
          isCurrentMonth: false,
          isPreviousMonth: true,
          isNextMonth: false
        });
      } else if (day <= daysInMonth) {
        // Current month
        const fullDate = year + '-' + (month + 1).toString().padStart(2, '0') + '-' + day.toString().padStart(2, '0');
        week.push({
          day: day,
          fullDate,
          isRestDay: j === 0 || j === 6,
          isToday: day === currentDay && month === currentMonth && year === currentYear,
          isCurrentMonth: true,
          isPreviousMonth: false,
          isNextMonth: false
        });
        day++;
      } else {
        // Next month
        const nextMonth = inputDate.add(1, 'month');
        const fullDate = nextMonth.year() + '-' + (nextMonth.month() + 1).toString().padStart(2, '0') + '-' + nextMonthDay.toString().padStart(2, '0');
        week.push({
          day: nextMonthDay,
          fullDate,
          isRestDay: j === 0 || j === 6,
          isToday: false,
          isCurrentMonth: false,
          isPreviousMonth: false,
          isNextMonth: true
        });
        nextMonthDay++;
      }
    }
    calendar.push(week);
    if (day > daysInMonth && nextMonthDay > 7) break; // Stop if we've filled all weeks
  }

  return {
    weekDays,
    calendar
  };
}

// 动态生成日历，支持跨月和指定生成周数
export function generateCalendar(date, weeksToGenerate = 6,customToday) {
    const inputDate = dayjs(date);  // 输入的日期
  const today = customToday ?? dayjs();  // 当前日期

  const month = inputDate.month();  // 输入日期的月份
  const calendar = [];

  // 找到输入日期所在周的周日 (一周从周日开始)
  const startOfWeek = inputDate.startOf('week'); // 获取指定日期所在周的周日
  let currentDate = startOfWeek;  // 从周日开始填充日历

  // 从指定日期生成日历，跨周跨月处理
  for (let i = 0; i < weeksToGenerate; i++) {
    const week = [];
    for (let j = 0; j < 7; j++) {
      const dayInMonth = currentDate.date();  // 当前处理的日期号
      const currentMonth = currentDate.month();  // 当前处理的月份
      const isToday = currentDate.isSame(today, 'day');  // 是否为今天
      const isPrevMonth = currentMonth < month;  // 是否为上个月
      const isNextMonth = currentMonth > month;  // 是否为下个月

      week.push({
        day: dayInMonth,  // 日期号
        fullDate: currentDate.format("YYYY-MM-DD"),
        isRestDay: j === 0 || j === 6,  // 判断是否是周末
        isToday: isToday,  // 是否是今天
        isCurrentMonth: !isPrevMonth && !isNextMonth,  // 当前月
        isPreviousMonth: isPrevMonth,  // 上个月
        isNextMonth: isNextMonth,  // 下个月
      });

      currentDate = currentDate.add(1, 'day');  // 增加一天，进入下一个日期
    }
    calendar.push(week);  // 将一周的日期推入到calendar中
  }

  return {
    weekDays,  // 一周的天数
    calendar   // 返回的日历矩阵
  };
}

export function generateEventCalendar(date){
  const selectedDate = dayjs(date)
  let result = []
  const firstDay = selectedDate.startOf('month').format("DD")
  const lastDay = selectedDate.endOf('month').format("DD")

  for (let i = 4; i >= -10; i--){
    const calcDate = selectedDate.subtract(i,'day')
    const weekDay = calcDate.day()
    const day = calcDate.format("DD")
    result.push({
      day: [firstDay, lastDay].includes(day) ? calcDate.format("MM-DD") : day,
      weekDay: `周${weekDays[weekDay]}`,
      fullDate: calcDate.format("YYYY-MM-DD"),
      isRestDay: [0,6].includes(weekDay),
      isToday: calcDate.isSame(selectedDate),
      isCurrentMonth: calcDate.isSame(selectedDate,'month'),
      isPreviousMonth: calcDate.isAfter(selectedDate,'month'),
      isNextMonth: calcDate.isBefore(selectedDate,'month'),
      visibleEvent: false
    })
  }

  return result
}
