import request from "@/utils/request";

export const getTaskUnitPage = (params) =>
  request({
    method: "get",
    url: "/taskUnit/listByPage",
    params,
  });

// 获取任务单元列表
export const getTaskUnitList = () =>
  request({
    method: "get",
    url: "/taskUnit/list",
  });

// 保存任务单元
export const saveTaskUnit = (data) =>
  request({
    method: "post",
    url: "/taskUnit/save",
    data,
  });

// 删除任务单元
export const deleteTaskUnit = (params) =>
  request({
    method: "get",
    url: "/taskUnit/delete",
    params,
  });

// 获取任务单元
export const getTaskUnit = (params) =>
  request({
    method: "get",
    url: "/taskUnit/getById",
    params,
  });

// 复制任务单元
export const copyTaskUnit = (params) =>
  request({
    method: "get",
    url: "/taskUnit/copy",
    params,
  });

// 获取岗位人员的树状表
export const getOrgTree = () =>
  request({
    method: "get",
    url: "/systemOrg/treeOrgOnlyPost",
  });

// 通过任务单元创建任务
export const createTaskByUnitId = (params) =>
  request({
    method: "get",
    url: "/taskCenter/createByUnitId",
    params,
  });

export const changeTaskUnitStatus = (params) =>
  request({
    method: "get",
    url: "/taskUnit/changeStatus",
    params,
  });
