import request from "@/utils/request";

// 获取全部规则
export const getAllRuleList = (params) =>
  request({
    method: "get",
    url: "/tradeType/listByPage",
    params,
  });

// 添加规则
export const saveRule = (data) =>
  request({
    method: "post",
    url: "/tradeType/save",
    data,
  });

// 获取规则详情
export const getRuleDetail = (id) =>
  request({
    method: "get",
    url: "/tradeType/detail",
    params: { id },
  });

// 删除规则
export const removeRule = (id) =>
  request({
    method: "post",
    url: "/tradeType/delete",
    data: { id },
  });

// 初始化日历
export const initCalendar = (params) =>
  request({
    method: "get",
    url: "/calendar/init",
    params,
  });

// 根据节假日更新日历
export const editCalendar = (data) =>
  request({
    method: "post",
    url: "/calendar/edit",
    data,
  });

// 查询已经初始化的年份
export const getCalendarExistYear = () =>
  request({
    method: "get",
    url: "/calendar/existYear",
  });
