export const getDownloadUrl = (url) => {
  return `${process.env.VUE_APP_ONLY_OFFICE_DOCKER_INTERNAL}/download/` + url;
};

export const getDownloadUrlSpecial = (url) => {
  return `${process.env.VUE_APP_ONLY_OFFICE_BASE}/download/` + url;
};

export const getLocalDownloadUrl = (url, filename) => {
  return (
    `${process.env.VUE_APP_ONLY_OFFICE_BASE}/download/` +
    url +
    (filename ? `?filename=${filename}.docx` : "")
  );
};

export const getLoadUrl = (url) => {
  return `${process.env.VUE_APP_ONLY_OFFICE_BASE}/loadFile/` + url;
};

export const getHtml = async (url) => {
  console.log(process.env);
  return new Promise((resolve) => {
    fetch(`${process.env.VUE_APP_ONLY_OFFICE_BASE}/convert`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json", // 请求头，表明发送 JSON 数据
      },
      body: JSON.stringify({ url: getDownloadUrlSpecial(url) }),
    }).then((res) => {
      resolve(res.json());
    });
  });
};

export const getOnlyOfficeAutoSave = () => {
  return `${process.env.VUE_APP_ONLY_OFFICE_DOCKER_INTERNAL}/onlyoffice/save`;
};

export const getFileName = (url) => {
  return url.split("/").at(-1);
};

export const getDefaultDoc = () => {
  return `${process.env.VUE_APP_ONLY_OFFICE_DOCKER_INTERNAL}/download/default_empty.docx`;
};

export const quickUpload = () => {
  return `${process.env.VUE_APP_ONLY_OFFICE_BASE}/upload`;
};
