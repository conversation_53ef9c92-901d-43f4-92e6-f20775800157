import { http } from './requset.js'
import service from './index'

// 监控告警&流程提醒&气泡
export function getAllData (userName) {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/message/monitor-result/query?userName=${userName}`
  })
}
// 消灭气泡
export function setRead (data) {
  return http.request({
    method: 'POST',
    url: `${service.serviceContext}/message/monitor-result/read`,
    data
  })
}
