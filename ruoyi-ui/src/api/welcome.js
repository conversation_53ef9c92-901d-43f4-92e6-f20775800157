import { http } from './requset'
import service from './index'
// import service from './index'
// 获取欢迎页信息
export function getWelcome () {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/welcome`,
  })
}

// 获取头像
export function getAvatars() {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/welcome/showUserAvatar`,
  })
}

// 上传头像
export function uploadAvatar(data) {
  return http.request({
    method: 'POST',
    url: `${service.serviceContext}/welcome/uploadUserAvatar`,
    data,
  })
}

// 更新头像
export function updateAcatar(params) {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/welcome/updateUserAvatar`,
    params,
  })
}

// 删除头像
export function deleteAcatar(params) {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/welcome/deleteUserAvatar`,
    params,
  })
}
