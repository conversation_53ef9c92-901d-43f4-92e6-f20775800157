package com.ruoyi.workflow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程表单历史数据对象 wf_form_history
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@TableName("wf_form_history")
public class WfFormHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 操作类型（REVOKE-撤回，RETURN-退回）
     */
    private String operationType;

    /**
     * 撤回次数
     */
    private Integer revokeCount;

    /**
     * 表单数据（JSON格式）
     */
    private String formData;

    /**
     * 操作用户ID
     */
    private String operatorId;

    /**
     * 操作用户名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private java.util.Date operationTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
