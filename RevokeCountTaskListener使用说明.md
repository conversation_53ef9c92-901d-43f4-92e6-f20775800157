# RevokeCountTaskListener 计数器监听器使用说明

## 概述

RevokeCountTaskListener 是一个简单的计数器任务监听器。每次触发时，它会对指定的流程变量进行累加操作，实现简单而高效的计数功能，供后续流程节点使用。

## 功能特性

### 1. 简单的计数器逻辑
- 每次触发监听器时，计数器自动加1（或指定增量值）
- 无需查询数据库，性能优异
- 自动处理异常情况，确保流程正常执行

### 2. 可配置的流程变量
- **variableName**: 自定义变量名（默认：revokeCount）
- **incrementValue**: 每次触发的增量值（默认：1）

### 3. 完善的错误处理
- 异常不会影响正常流程执行
- 详细的日志记录
- 默认值处理机制

## 在 Flowable 流程中的配置

### 1. 基本配置

使用默认配置，每次触发计数器加1：

```xml
<userTask id="checkTask" name="检查任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 2. 自定义变量名配置

将计数器保存到自定义变量名：

```xml
<userTask id="approvalTask" name="审批任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>approvalCount</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 3. 自定义增量值配置

每次触发时增加指定的数值：

```xml
<userTask id="countTask" name="计数任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>stepCount</flowable:string>
      </flowable:field>
      <flowable:field name="incrementValue">
        <flowable:string>2</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 4. 完整配置示例

包含所有配置选项的完整示例：

```xml
<userTask id="customCountTask" name="自定义计数任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>customCounter</flowable:string>
      </flowable:field>
      <flowable:field name="incrementValue">
        <flowable:string>5</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

## 配置参数详细说明

### variableName（变量名）
- **类型**: String
- **默认值**: `revokeCount`
- **说明**: 设置计数器在流程变量中的名称
- **示例**: `totalCount`、`stepCount`、`operationCount`

### incrementValue（增量值）
- **类型**: Integer
- **默认值**: `1`
- **说明**: 每次触发监听器时的增量值
- **示例**: `1`、`2`、`5`、`10`

## 计数器工作原理

### 计数器逻辑
```java
// 获取当前计数器值（如果不存在则初始化为1）
Integer currentCount = (Integer) runtimeService.getVariable(processInstanceId, variableName);
if (currentCount == null) {
    currentCount = 1;  // 初始值为1
}

// 计算新值
int newCount = currentCount + incrementValue;

// 设置回流程变量
runtimeService.setVariable(processInstanceId, variableName, newCount);
```

### 计数示例
- **第1次触发**: 变量不存在 → 初始化为1 → 1 + 1 = 2
- **第2次触发**: 当前值为2 → 2 + 1 = 3
- **第3次触发**: 当前值为3 → 3 + 1 = 4
- **以此类推...**

## 在后续流程中使用变量

### 1. 在网关条件中使用

```xml
<exclusiveGateway id="countCheckGateway" name="计数器检查">
</exclusiveGateway>

<sequenceFlow id="normalFlow" sourceRef="countCheckGateway" targetRef="normalTask">
  <conditionExpression xsi:type="tFormalExpression">
    ${revokeCount &lt; 3}
  </conditionExpression>
</sequenceFlow>

<sequenceFlow id="excessiveFlow" sourceRef="countCheckGateway" targetRef="excessiveTask">
  <conditionExpression xsi:type="tFormalExpression">
    ${revokeCount &gt;= 3}
  </conditionExpression>
</sequenceFlow>
```

### 2. 在任务分配中使用

```xml
<userTask id="escalationTask" name="升级处理" flowable:assignee="${revokeCount > 2 ? 'manager' : 'operator'}">
</userTask>
```

### 3. 在脚本任务中使用

```xml
<scriptTask id="logTask" name="记录日志" scriptFormat="groovy">
  <script>
    def count = execution.getVariable("revokeCount")
    println "当前流程撤回次数: " + count
    
    if (count > 5) {
      execution.setVariable("needManagerApproval", true)
    }
  </script>
</scriptTask>
```

### 4. 在服务任务中使用

```java
@Component
public class ProcessService {
    
    public void handleTask(DelegateExecution execution) {
        Integer revokeCount = (Integer) execution.getVariable("revokeCount");
        
        if (revokeCount != null && revokeCount > 3) {
            // 撤回次数过多，发送通知
            sendNotification("流程撤回次数过多: " + revokeCount);
        }
    }
}
```

## 实际应用场景

### 1. 流程控制场景

```xml
<!-- 在关键节点检查撤回次数 -->
<userTask id="importantApproval" name="重要审批">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>currentRevokeCount</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>

<!-- 根据撤回次数决定后续流程 -->
<exclusiveGateway id="revokeDecision">
</exclusiveGateway>

<sequenceFlow sourceRef="revokeDecision" targetRef="normalProcess">
  <conditionExpression>${currentRevokeCount &lt; 2}</conditionExpression>
</sequenceFlow>

<sequenceFlow sourceRef="revokeDecision" targetRef="managerReview">
  <conditionExpression>${currentRevokeCount &gt;= 2}</conditionExpression>
</sequenceFlow>
```

### 2. 权限升级场景

```xml
<!-- 计算退回次数 -->
<userTask id="reviewTask" name="审核任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>returnCount</flowable:string>
      </flowable:field>
      <flowable:field name="operationTypeFilter">
        <flowable:string>RETURN</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>

<!-- 动态分配审批人 -->
<userTask id="dynamicApproval" name="动态审批" 
          flowable:assignee="${returnCount > 1 ? 'senior_manager' : 'manager'}">
</userTask>
```

### 3. 监控告警场景

```xml
<!-- 监控撤回次数 -->
<userTask id="monitorTask" name="监控任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>totalOperations</flowable:string>
      </flowable:field>
      <flowable:field name="includeCurrent">
        <flowable:string>true</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>

<!-- 告警服务任务 -->
<serviceTask id="alertTask" name="告警检查" 
             flowable:delegateExpression="${alertService}">
</serviceTask>
```

## 最佳实践

### 1. 变量命名规范
- 使用有意义的变量名：`revokeCount`、`returnCount`、`operationCount`
- 避免与系统变量冲突
- 保持命名一致性

### 2. 性能考虑
- 在必要的节点使用，避免过度使用
- 考虑数据库查询性能
- 合理设置操作类型过滤

### 3. 错误处理
- 监听器异常不会影响流程执行
- 关注日志中的错误信息
- 设置合理的默认值

### 4. 测试验证
- 验证变量设置是否正确
- 测试不同操作类型的统计
- 确认后续流程能正确使用变量

## 总结

RevokeCountTaskListener 提供了一个灵活、可配置的方式来计算和使用流程实例的撤回次数。通过将计算结果设置为流程变量，可以在后续的流程节点中进行条件判断、权限控制、监控告警等操作，大大增强了流程的智能化和自动化程度。
