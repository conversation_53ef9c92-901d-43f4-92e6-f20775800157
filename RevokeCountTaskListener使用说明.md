# RevokeCountTaskListener 撤回次数计算监听器使用说明

## 概述

RevokeCountTaskListener 是一个专门用于计算当前流程实例撤回次数的 Flowable 任务监听器。它会统计流程实例的历史操作次数，并将结果作为流程变量设置，供后续流程节点使用。

## 功能特性

### 1. 灵活的撤回次数计算
- 支持统计所有操作类型或特定操作类型
- 可选择是否包含当前操作
- 自动处理异常情况，确保流程正常执行

### 2. 可配置的流程变量
- **variableName**: 自定义变量名（默认：revokeCount）
- **includeCurrent**: 是否包含当前操作（默认：false）
- **operationTypeFilter**: 操作类型过滤（默认：ALL）

### 3. 完善的错误处理
- 异常不会影响正常流程执行
- 详细的日志记录
- 默认值处理机制

## 在 Flowable 流程中的配置

### 1. 基本配置

统计所有操作次数并设置为默认变量名：

```xml
<userTask id="checkTask" name="检查任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 2. 自定义变量名配置

将撤回次数保存到自定义变量名：

```xml
<userTask id="approvalTask" name="审批任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>totalRevokeCount</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 3. 包含当前操作配置

统计包含当前操作在内的总次数：

```xml
<userTask id="countTask" name="计数任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>currentRevokeCount</flowable:string>
      </flowable:field>
      <flowable:field name="includeCurrent">
        <flowable:string>true</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 4. 操作类型过滤配置

只统计特定类型的操作：

```xml
<userTask id="returnCountTask" name="退回次数统计">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>returnCount</flowable:string>
      </flowable:field>
      <flowable:field name="operationTypeFilter">
        <flowable:string>RETURN</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

### 5. 完整配置示例

包含所有配置选项的完整示例：

```xml
<userTask id="complexCountTask" name="复杂计数任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>revokeOperationCount</flowable:string>
      </flowable:field>
      <flowable:field name="includeCurrent">
        <flowable:string>false</flowable:string>
      </flowable:field>
      <flowable:field name="operationTypeFilter">
        <flowable:string>REVOKE</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

## 配置参数详细说明

### variableName（变量名）
- **类型**: String
- **默认值**: `revokeCount`
- **说明**: 设置撤回次数在流程变量中的名称
- **示例**: `totalCount`、`revokeNumber`、`operationCount`

### includeCurrent（包含当前操作）
- **类型**: Boolean
- **默认值**: `false`
- **说明**: 是否将当前操作计入撤回次数
- **可选值**: `true`、`false`

### operationTypeFilter（操作类型过滤）
- **类型**: String
- **默认值**: `ALL`
- **说明**: 过滤特定类型的操作进行统计
- **可选值**: 
  - `ALL`: 统计所有操作类型
  - `RETURN`: 仅统计退回操作
  - `REVOKE`: 仅统计撤回操作
  - `TRIGGER`: 仅统计触发器操作

## 在后续流程中使用变量

### 1. 在网关条件中使用

```xml
<exclusiveGateway id="revokeCheckGateway" name="撤回次数检查">
</exclusiveGateway>

<sequenceFlow id="normalFlow" sourceRef="revokeCheckGateway" targetRef="normalTask">
  <conditionExpression xsi:type="tFormalExpression">
    ${revokeCount &lt; 3}
  </conditionExpression>
</sequenceFlow>

<sequenceFlow id="excessiveFlow" sourceRef="revokeCheckGateway" targetRef="excessiveTask">
  <conditionExpression xsi:type="tFormalExpression">
    ${revokeCount &gt;= 3}
  </conditionExpression>
</sequenceFlow>
```

### 2. 在任务分配中使用

```xml
<userTask id="escalationTask" name="升级处理" flowable:assignee="${revokeCount > 2 ? 'manager' : 'operator'}">
</userTask>
```

### 3. 在脚本任务中使用

```xml
<scriptTask id="logTask" name="记录日志" scriptFormat="groovy">
  <script>
    def count = execution.getVariable("revokeCount")
    println "当前流程撤回次数: " + count
    
    if (count > 5) {
      execution.setVariable("needManagerApproval", true)
    }
  </script>
</scriptTask>
```

### 4. 在服务任务中使用

```java
@Component
public class ProcessService {
    
    public void handleTask(DelegateExecution execution) {
        Integer revokeCount = (Integer) execution.getVariable("revokeCount");
        
        if (revokeCount != null && revokeCount > 3) {
            // 撤回次数过多，发送通知
            sendNotification("流程撤回次数过多: " + revokeCount);
        }
    }
}
```

## 实际应用场景

### 1. 流程控制场景

```xml
<!-- 在关键节点检查撤回次数 -->
<userTask id="importantApproval" name="重要审批">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>currentRevokeCount</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>

<!-- 根据撤回次数决定后续流程 -->
<exclusiveGateway id="revokeDecision">
</exclusiveGateway>

<sequenceFlow sourceRef="revokeDecision" targetRef="normalProcess">
  <conditionExpression>${currentRevokeCount &lt; 2}</conditionExpression>
</sequenceFlow>

<sequenceFlow sourceRef="revokeDecision" targetRef="managerReview">
  <conditionExpression>${currentRevokeCount &gt;= 2}</conditionExpression>
</sequenceFlow>
```

### 2. 权限升级场景

```xml
<!-- 计算退回次数 -->
<userTask id="reviewTask" name="审核任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>returnCount</flowable:string>
      </flowable:field>
      <flowable:field name="operationTypeFilter">
        <flowable:string>RETURN</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>

<!-- 动态分配审批人 -->
<userTask id="dynamicApproval" name="动态审批" 
          flowable:assignee="${returnCount > 1 ? 'senior_manager' : 'manager'}">
</userTask>
```

### 3. 监控告警场景

```xml
<!-- 监控撤回次数 -->
<userTask id="monitorTask" name="监控任务">
  <extensionElements>
    <flowable:taskListener event="create" class="com.ruoyi.flowable.listener.RevokeCountTaskListener">
      <flowable:field name="variableName">
        <flowable:string>totalOperations</flowable:string>
      </flowable:field>
      <flowable:field name="includeCurrent">
        <flowable:string>true</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>

<!-- 告警服务任务 -->
<serviceTask id="alertTask" name="告警检查" 
             flowable:delegateExpression="${alertService}">
</serviceTask>
```

## 最佳实践

### 1. 变量命名规范
- 使用有意义的变量名：`revokeCount`、`returnCount`、`operationCount`
- 避免与系统变量冲突
- 保持命名一致性

### 2. 性能考虑
- 在必要的节点使用，避免过度使用
- 考虑数据库查询性能
- 合理设置操作类型过滤

### 3. 错误处理
- 监听器异常不会影响流程执行
- 关注日志中的错误信息
- 设置合理的默认值

### 4. 测试验证
- 验证变量设置是否正确
- 测试不同操作类型的统计
- 确认后续流程能正确使用变量

## 总结

RevokeCountTaskListener 提供了一个灵活、可配置的方式来计算和使用流程实例的撤回次数。通过将计算结果设置为流程变量，可以在后续的流程节点中进行条件判断、权限控制、监控告警等操作，大大增强了流程的智能化和自动化程度。
