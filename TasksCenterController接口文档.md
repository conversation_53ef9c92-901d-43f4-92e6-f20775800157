# TasksCenterController 接口文档

## 概述

TasksCenterController 是任务中心的核心控制器，负责任务的创建、查询、状态管理、转派、复核等全生命周期操作。该控制器集成了任务模板、任务单元、任务实例的管理功能。

**请求路径基础**: `/taskCenter`

## 任务模板创建任务流程详解

### 流程概述

任务模板创建任务是系统的核心功能之一，支持将预设的任务模板转换为可执行的任务实例。整个流程包括模板设计、数据转换、任务生成和调度配置四个主要阶段。

### 数据流转图

```
OpsTaskTemplate (模板基础信息)
     +
OpsTaskAttrBasicReplica (任务单元副本，树状结构)
     ↓
扁平化处理 (flattenHandlerSelf)
     ↓
ID重映射 (replaceIdAndFillChildIdsAndSort)
     ↓
数据转换 (convertGenInfo)
     ↓
OpsTaskGenInfo (任务实例，可执行)
     ↓
批量保存到数据库
```

### 详细流程步骤

#### 第一步：模板数据准备
1. **模板基础信息** (`OpsTaskTemplate`)
   - 模板ID、名称、类型
   - 调度类型 (`scheduler_type`): `manual`(立即生成)、`daily`(日常任务)
   - 权限控制 (`org_id`)
   - 模板状态 (`template_status`)

2. **任务单元副本列表** (`OpsTaskAttrBasicReplica`)
   - 树状结构的任务配置
   - 包含父子关系、依赖关系
   - 每个节点包含完整的任务属性

#### 第二步：数据扁平化处理
```java
// 递归处理树状结构，转为平面列表
List<OpsTaskAttrBasicReplica> flattens = new ArrayList<>();
flattenHandlerSelf(vo.getList(), flattens);
```
- 保持父子关系信息
- 维护任务依赖关系
- 确保排序规则

#### 第三步：ID重映射和关系维护
```java
// 生成新的唯一ID，保持关系完整性
replaceIdAndFillChildIdsAndSort(saveBatch);
```
- 为每个任务分配新的雪花算法ID
- 更新父子关系映射
- 重建依赖关系链
- 聚合子节点ID列表

#### 第四步：数据转换
```java
// 将模板配置转换为任务实例
List<OpsTaskGenInfo> saveBatch = flattens.stream()
    .map(this::convertGenInfo).collect(Collectors.toList());
```

**转换规则**：
- **基础状态设置**：
  - `task_process_status` = 1 (正常)
  - `task_complete_status` = 1 (进行中)
  - `task_ref` = 3 (任务模板来源)
  - `task_transfer_status` = 0 (未转派)

- **任务类型处理**：
  - 根据模板配置确定任务类型
  - 处理时间计算（工作日、周期等）
  - 任务名称追加（年、季度、月等标识）

- **权限继承**：
  - 任务归属信息复制
  - 复核权限配置继承
  - 查看权限规则应用

#### 第五步：调度类型处理

##### Manual类型（立即生成）
```java
if (Objects.equals(TaskConstant.MANUAL, tempType)) {
    // 立即生成任务实例
    saveBatch(saveBatch);
    // 处理提醒和消息提示
    processRemindersAndTips(flattens, saveBatch);
}
```

##### Daily类型（日常任务）
```java
if (Objects.equals(TaskConstant.TASK_TYPE_DAILY, tempType)) {
    // 创建任务关联关系，由定时器触发
    createDailyTaskRelations(flattens);
}
```

#### 第六步：扩展功能处理

##### 任务提醒设置
```java
// 创建任务提醒记录
if (remind != null && remind) {
    OpsTaskReminder reminder = new OpsTaskReminder()
        .setTaskId(taskInfo.getId());
    opsTaskReminderService.save(reminder);
}
```

##### 消息提示设置
```java
// 创建消息提示记录
if (StringUtils.hasText(tipDate)) {
    OpsTaskMessageTip tip = new OpsTaskMessageTip()
        .setTipDate(tipDate)
        .setMessage(tipMessage)
        .setTaskOwnerId(taskInfo.getTaskOwnerId())
        .setTipType(tipType);
    opsTaskMessageTipService.save(tip);
}
```

### 关键技术特点

#### 1. 事务一致性
- 使用 `@Transactional(rollbackFor = Exception.class)` 确保数据一致性
- 异常时自动回滚所有操作

#### 2. 性能优化
- **批量操作**：`saveBatch()` 批量插入任务
- **流式处理**：使用 Stream API 进行数据转换
- **内存效率**：扁平化处理减少递归开销

#### 3. 数据完整性
- **ID映射**：确保父子关系和依赖关系完整性
- **状态标准化**：统一的状态字段设置
- **冗余字段**：关键信息冗余存储，提高查询效率

#### 4. 扩展性设计
- **插件化提醒**：独立的提醒和消息模块
- **调度类型扩展**：支持多种任务调度模式
- **权限体系集成**：与系统权限框架无缝集成

### 使用场景

#### 1. 日常运维任务
- 每日数据备份任务模板
- 系统监控检查任务模板
- 定期报表生成任务模板

#### 2. 项目管理
- 项目里程碑任务模板
- 质量检查任务模板
- 上线部署任务模板

#### 3. 合规管理
- 内控检查任务模板
- 风险评估任务模板
- 审计流程任务模板

### 最佳实践

#### 1. 模板设计原则
- **模块化**：将复杂流程拆解为独立任务单元
- **层次化**：合理设计父子任务关系
- **可复用**：设计通用性强的任务模板

#### 2. 权限配置建议
- **最小权限**：只授予必要的任务执行权限
- **分层复核**：重要任务设置多级复核
- **责任分离**：执行和复核权限分离

#### 3. 时间规划要点
- **工作日考虑**：合理设置任务开始和截止时间
- **缓冲时间**：为任务执行预留适当缓冲
- **依赖关系**：确保前置任务有足够完成时间

### 常见问题和解决方案

#### 1. 模板创建失败
- **检查权限**：确认模板创建者有相应权限
- **验证数据**：检查模板和副本数据完整性
- **查看日志**：分析异常堆栈定位具体问题

#### 2. 任务关系错乱
- **ID映射检查**：验证父子关系映射是否正确
- **依赖链验证**：确认任务依赖关系无循环
- **排序问题**：检查任务排序字段设置

#### 3. 性能问题
- **批量大小**：控制单次批量操作的数据量
- **索引优化**：确保相关查询字段有合适索引
- **缓存策略**：对频繁查询的模板数据进行缓存

## 核心表结构

### 主要数据表
- **ops_task_gen_info**: 任务生成信息表（核心表）
- **ops_task_attr_basic**: 任务属性基础表（任务单元配置）
- **ops_task_template**: 任务模板表
- **ops_task_attr_basic_replica**: 任务属性基础副本表
- **ops_task_reminder**: 任务提醒表
- **ops_task_message_tip**: 任务消息提示表

### 核心实体类详情

#### OpsTaskGenInfo - 任务生成信息表
**表名**: `ops_task_gen_info`

**核心字段**:
- `id` (BIGINT): 主键，雪花算法生成
- `parent_id` (BIGINT): 父任务ID，默认根节点为0
- `task_no` (VARCHAR): 任务顺序编号，如1-1、1-2、1-3
- `task_name` (VARCHAR): 任务名称
- `task_type` (VARCHAR): 任务类型 (daily-日常任务, period-周期任务, temp-临时任务)
- `task_process_status` (INTEGER): 任务状态 (1-正常, 2-异常)
- `task_complete_status` (INTEGER): 任务完成状态 (0-未完成, 1-进行中, 2-待复核, 3-已完成, 5-未发生)
- `task_complete_desc` (VARCHAR): 任务完成备注
- `task_ref` (INTEGER): 任务来源 (1-手动, 2-任务单元, 3-任务模板)

**权限和归属相关**:
- `task_owner_type` (CHAR): 任务归属类型 (1-岗位, 2-具体人员)
- `task_owner_id` (VARCHAR): 任务归属ID
- `task_owner_val` (VARCHAR): 任务归属真实值 (冗余字段)
- `owner_org_id` (VARCHAR): 冗余归属岗位ID

**复核相关**:
- `task_check_req` (CHAR): 是否需要复核 (0-否, 1-是)
- `task_check_type` (CHAR): 复核权限对象类型 (1-岗位, 2-人员)
- `task_check_id` (VARCHAR): 复核权限对象ID
- `task_check_val` (VARCHAR): 复核权限对象真实值
- `check_org_id` (VARCHAR): 冗余复核岗位ID
- `task_check_status` (VARCHAR): 复核状态 (0-未复核, 1-复核通过, 2-驳回)
- `task_check_desc` (VARCHAR): 复核结果备注

**时间相关**:
- `task_gen_time` (VARCHAR): 生成日期
- `task_start_time` (TIMESTAMP): 任务开始时间
- `task_end_time` (TIMESTAMP): 任务结束时间
- `complete_time` (TIMESTAMP): 完成时间
- `audit_time` (TIMESTAMP): 复核通过时间

**转派相关**:
- `task_transfer_status` (INTEGER): 经办转派状态 (0-未转派, 1-转派)
- `task_check_transfer_status` (INTEGER): 复核转派状态 (0-未转派, 1-转派)
- `task_transfer_user_id` (VARCHAR): 转派人ID
- `task_transfer_desc` (VARCHAR): 转派备注

**层级和关联**:
- `task_child_ids` (VARCHAR): 所有子级ID
- `task_ref_id` (VARCHAR): 任务生成来源的任务单元ID
- `task_bind_template_id` (VARCHAR): 任务绑定模板ID
- `task_sort` (INTEGER): 排序

## API 接口详情

### 1. 任务查询接口

#### 1.1 获取任务列表（简单版）
```http
GET /taskCenter/list
```
- **功能**: 获取与当前用户相关的任务列表
- **参数**: 无
- **返回**: `R<Object>`
- **状态**: 接口未实现，返回null

#### 1.2 获取任务列表（分页版）
```http
GET /taskCenter/listByPage
```
- **功能**: 分页查询任务列表，支持多种过滤条件
- **参数**:
  - `taskName` (String, 可选): 任务名称，支持模糊查询
  - `taskStatus` (String, 可选): 任务状态
  - `taskType` (String, 可选): 任务类型
  - `orgId` (String, 可选): 组织ID
  - `createTime` (Date, 可选): 创建时间，格式 yyyy-MM-dd
  - `page` (int, 默认1): 页码
  - `pageSize` (int, 默认10): 每页大小
- **返回**: 分页任务数据，包含父子任务层级结构
- **特性**: 
  - 支持权限过滤
  - 自动构建父子任务树形结构
  - 默认查询根节点任务（parent_id=0）

#### 1.3 获取任务详情
```http
GET /taskCenter/getById?id={taskId}
```
- **功能**: 根据ID获取单个任务详情
- **参数**: `id` (String, 必需): 任务ID
- **返回**: 任务详情对象

#### 1.4 获取模板任务详情
```http
GET /taskCenter/tempDetail?id={taskId}
```
- **功能**: 获取模板任务详情，包含子任务信息
- **参数**: `id` (String, 必需): 任务ID
- **返回**: 任务详情对象，包含children子任务数组

### 2. 任务创建接口

#### 2.1 通过模板创建任务
```http
POST /taskCenter/createByTemp
Content-Type: application/json

{
  "id": "任务单元ID",
  "taskName": "任务名称",
  "remind": true,
  "tipDate": "2024-01-01",
  "tipMessage": "提示消息",
  "tipType": 1
}
```
- **功能**: 基于任务模板创建新任务
- **请求体**: `OpsTaskAttrBasic` 对象
- **附加功能**:
  - 支持任务提醒设置（remind字段）
  - 支持消息提示设置（tipDate, tipMessage, tipType字段）
- **返回**: 操作成功信息

#### 2.2 通过模板创建用户任务
```http
POST /taskCenter/createByTempByXh
Content-Type: application/json

{
  "id": "任务单元ID",
  "taskName": "任务名称",
  "remind": true,
  "tipDate": "2024-01-01",
  "tipMessage": "提示消息",
  "tipType": 1
}
```
- **功能**: 基于模板为当前登录用户创建任务
- **请求体**: `OpsTaskAttrBasic` 对象
- **特性**: 
  - 自动设置任务归属为当前用户
  - task_create_type=1, task_owner_type="2"
  - 自动获取用户信息和部门信息

#### 2.3 通过任务单元创建任务
```http
POST /taskCenter/createByUnit
Content-Type: application/json

{
  "id": "任务单元ID",
  "taskName": "任务名称"
}
```
- **功能**: 基于任务单元创建任务
- **请求体**: `OpsTaskAttrBasic` 对象
- **返回**: 操作成功信息

#### 2.4 通过任务单元ID创建任务
```http
GET /taskCenter/createByUnitId?id={unitId}
```
- **功能**: 根据任务单元ID创建任务
- **参数**: `id` (String, 必需): 任务单元ID
- **返回**: 操作成功信息

#### 2.5 通过模板创建任务（高级版）
```http
POST /taskCenter/createByTemplate
Content-Type: application/json

{
  "template": {
    "id": "模板ID",
    "templateName": "模板名称"
  },
  "list": [
    {
      "id": "副本ID",
      "taskName": "任务名称"
    }
  ]
}
```
- **功能**: 基于完整模板配置创建任务
- **请求体**: `TemplateVO` 对象
- **返回**: 操作成功信息

#### 2.6 通过模板ID创建任务
```http
GET /taskCenter/createByTemplateId?id={templateId}
```
- **功能**: 根据模板ID创建任务
- **参数**: `id` (String, 必需): 模板ID
- **返回**: 操作成功信息

### 3. 任务状态管理接口

#### 3.1 任务完成
```http
POST /taskCenter/taskComplete
Content-Type: application/json

{
  "taskId": "任务ID",
  "taskDesc": "完成备注",
  "delay": "0",
  "workAmount": 1,
  "remind": true,
  "tipDate": "2024-01-01",
  "tipMessage": "提示消息",
  "tipType": 1
}
```
- **功能**: 标记任务为完成状态
- **请求体**: `TaskCompleteVO` 对象
- **验证逻辑**:
  - 检查必填附件是否上传
  - 检查前置依赖任务是否完成
  - 验证是否允许批量完成
- **返回**: 成功信息或具体错误信息
  - "1": 本任务附件未上传,不能操作
  - "2": 本任务依赖前置任务还未完成,不能操作
  - "4": 不允许批量完成

#### 3.2 任务未发生
```http
POST /taskCenter/taskNoExist
Content-Type: application/json

{
  "taskId": "任务ID",
  "taskDesc": "未发生原因"
}
```
- **功能**: 标记任务为未发生状态
- **请求体**: `TaskCompleteVO` 对象
- **返回**: 操作成功信息

#### 3.3 批量任务未发生
```http
POST /taskCenter/multiTaskNoExist
Content-Type: application/json

{
  "taskIds": ["任务ID1", "任务ID2"],
  "taskDesc": "未发生原因"
}
```
- **功能**: 批量标记任务为未发生状态
- **请求体**: `TaskCompleteVO` 对象
- **返回**: 操作成功信息

#### 3.4 任务重置
```http
GET /taskCenter/taskReset?id={taskId}
```
- **功能**: 重置任务状态为进行中，支持父子任务联动
- **参数**: `id` (String, 必需): 任务ID
- **返回**: 操作成功信息

#### 3.5 更新任务完成备注
```http
POST /taskCenter/taskDescUpdate
Content-Type: application/json

{
  "taskId": "任务ID",
  "taskDesc": "更新的完成备注"
}
```
- **功能**: 单独更新任务完成备注
- **请求体**: `TaskCompleteVO` 对象
- **返回**: 操作成功信息

### 4. 任务转派管理接口

#### 4.1 任务转派
```http
POST /taskCenter/taskTransfer
Content-Type: application/json

{
  "taskId": "任务ID",
  "orgId": "目标组织ID",
  "orgName": "目标组织名称",
  "userId": "目标用户ID",
  "userName": "目标用户名称",
  "taskDesc": "转派说明",
  "tranStartTime": "2024-01-01 00:00:00",
  "tranEndTime": "2024-01-31 23:59:59",
  "type": 1,
  "transferType": 1
}
```
- **功能**: 转派单个任务给其他用户或岗位
- **请求体**: `TaskTransferVO` 对象
- **权限要求**: 部门领导或管理员
- **参数说明**:
  - `type`: 1-到岗, 2-到人
  - `transferType`: 1-经办转派, 2-复核转派
- **返回**: 操作成功信息或权限错误

#### 4.2 批量任务转派
```http
POST /taskCenter/batchTaskTransfer
Content-Type: application/json

{
  "taskIds": ["任务ID1", "任务ID2"],
  "orgId": "目标组织ID",
  "userId": "目标用户ID",
  "taskDesc": "批量转派说明"
}
```
- **功能**: 批量转派任务
- **请求体**: `TaskTransferVO` 对象
- **权限要求**: 部门领导或管理员
- **返回**: 操作成功信息或权限错误

### 5. 任务复核管理接口

#### 5.1 获取复核列表
```http
GET /taskCenter/auditList?taskName=任务名&taskStatus=2&page=1&pageSize=10
```
- **功能**: 获取待复核任务列表
- **参数**: 与listByPage相同的查询参数
- **权限**: 基于用户岗位权限过滤
- **返回**: 待复核任务列表

#### 5.2 任务复核
```http
POST /taskCenter/taskAudit
Content-Type: application/json

{
  "taskId": "任务ID",
  "taskDesc": "复核意见",
  "checkResult": "1"
}
```
- **功能**: 对任务进行复核操作
- **请求体**: `TaskCheckVO` 对象
- **权限要求**: 具有复核岗位权限或管理员
- **参数说明**:
  - `checkResult`: 1-复核通过, 2-驳回
- **返回**: 操作成功信息或权限错误

#### 5.3 批量任务复核
```http
POST /taskCenter/batchTaskAudit
Content-Type: application/json

{
  "taskIds": ["任务ID1", "任务ID2"],
  "taskDesc": "批量复核意见",
  "checkResult": "1"
}
```
- **功能**: 批量复核任务
- **请求体**: `TaskCheckVO` 对象
- **权限要求**: 具有复核岗位权限或管理员
- **返回**: 操作成功信息或权限错误

### 6. 任务历史查询接口

#### 6.1 历史完成备注
```http
GET /taskCenter/taskHisCompleteDesc?refId={refId}
```
- **功能**: 查询任务历史完成备注信息（近一年）
- **参数**: `refId` (String, 可选): 任务引用ID
- **返回**: 历史完成备注列表（去重）

#### 6.2 历史复核备注
```http
GET /taskCenter/taskHisCheckDesc?refId={refId}
```
- **功能**: 查询任务历史复核备注信息（近一年）
- **参数**: `refId` (String, 必需): 任务引用ID
- **返回**: 历史复核备注列表（去重）

### 7. 任务维护接口

#### 7.1 更新任务描述
```http
POST /taskCenter/rewriteTaskDesc
Content-Type: application/json

{
  "id": "任务ID",
  "taskDesc": "新的任务描述"
}
```
- **功能**: 修改任务描述，同步更新来源任务单元
- **请求体**: `OpsTaskGenInfo` 对象（包含id和taskDesc）
- **事务**: 支持事务回滚
- **返回**: 操作成功信息

#### 7.2 更新任务截止时间
```http
POST /taskCenter/editTaskEndTime
Content-Type: application/json

{
  "id": "任务ID",
  "taskEndTime": "2024-12-31 23:59:59"
}
```
- **功能**: 修改单个任务的截止时间
- **请求体**: `OpsTaskGenInfo` 对象（包含id和taskEndTime）
- **权限要求**: 具有岗位权限或管理员
- **返回**: 操作成功信息或权限错误

#### 7.3 删除任务
```http
GET /taskCenter/deleteTask?id={taskId}
```
- **功能**: 删除任务及其所有子任务
- **参数**: `id` (String, 必需): 任务ID
- **权限要求**: 具有岗位权限或管理员
- **特性**: 自动删除关联的所有子任务
- **返回**: 操作成功信息或权限错误

### 8. 特殊功能接口

#### 8.1 代办任务列表
```http
GET /taskCenter/todoList?date=2024-01-01
```
- **功能**: 获取当前用户的代办任务和日程安排
- **参数**: `date` (String, 可选): 查询日期
- **返回**: 包含todoList和arrange的组合对象
```json
{
  "code": 200,
  "data": {
    "todoList": [
      {
        "id": "任务ID",
        "taskName": "任务名称",
        "taskEndTime": "2024-01-01 18:00:00",
        "reminder": 1
      }
    ],
    "arrange": [
      {
        "id": "日程ID",
        "arrangeName": "日程名称",
        "arrangeTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```
- **特性**: 
  - 查询临时任务类型（temp）
  - 基于时间范围过滤
  - 包含任务提醒信息
  - 集成日程安排信息

#### 8.2 第三方数据列表
```http
GET /taskCenter/thirdList
```
- **功能**: 查询第三方抓取但无法处理的数据源信息
- **参数**: 无（自动获取当前用户ID）
- **返回**: 未处理的第三方数据列表

#### 8.3 分配任务
```http
POST /taskCenter/assignTask?dataId={dataId}&taskId={taskId}&triggerId={triggerId}
```
- **功能**: 将第三方数据分配给具体任务
- **参数**: 
  - `dataId` (String, 必需): 数据ID
  - `taskId` (String, 必需): 任务ID
  - `triggerId` (String, 可选, 默认-1): 触发器ID
- **返回**: 操作完成信息
- **异常**: 可能抛出TaskException

#### 8.4 日常任务执行测试
```http
GET /taskCenter/dailTaskExecute
```
- **功能**: 测试执行日常任务调度
- **参数**: 无
- **返回**: 执行成功信息
- **用途**: 用于测试和手动触发日常任务调度

## 权限控制机制

### 权限类型
1. **个人权限** (type=1): 用户只能操作自己的任务
2. **岗位权限** (type=2): 基于用户所属岗位查看和操作任务
3. **部门领导权限**: 可以转派和管理部门内任务
4. **管理员权限** (type=4): 拥有所有操作权限

### 权限验证流程
1. 调用 `taskSpecialAuthFilter()` 获取权限条件
2. 根据 `ConditionTaskDTO.type` 判断权限级别
3. 在查询和操作中应用权限过滤条件

### ConditionTaskDTO 结构
```json
{
  "userId": "用户ID",
  "postIds": ["岗位ID1", "岗位ID2"],
  "type": 1
}
```

## 任务状态流转

### 完成状态 (task_complete_status)
- **0**: 未完成
- **1**: 进行中  
- **2**: 待复核
- **3**: 已完成
- **5**: 未发生

### 复核状态 (task_check_status)
- **0**: 未复核
- **1**: 复核通过
- **2**: 驳回

### 转派状态
- **task_transfer_status**: 经办转派状态 (0-未转派, 1-转派)
- **task_check_transfer_status**: 复核转派状态 (0-未转派, 1-转派)

### 状态流转图
```
未完成(0) → 进行中(1) → 待复核(2) → 已完成(3)
                     ↓
                   未发生(5)
```

## VO类详细说明

### TaskCompleteVO
```json
{
  "taskId": "任务ID",
  "taskIds": ["任务ID1", "任务ID2"],
  "taskDesc": "完成备注",
  "delay": "0",
  "workAmount": 1,
  "remind": true,
  "tipDate": "2024-01-01",
  "tipMessage": "提示消息",
  "tipType": 1
}
```

### TaskTransferVO
```json
{
  "taskId": "任务ID",
  "taskIds": ["任务ID1", "任务ID2"],
  "orgId": "目标组织ID",
  "orgName": "目标组织名称",
  "userId": "目标用户ID", 
  "userName": "目标用户名称",
  "taskDesc": "转派说明",
  "tranStartTime": "2024-01-01 00:00:00",
  "tranEndTime": "2024-01-31 23:59:59",
  "type": 1,
  "transferType": 1
}
```

### TaskCheckVO
```json
{
  "taskId": "任务ID",
  "taskIds": ["任务ID1", "任务ID2"],
  "taskDesc": "复核意见",
  "checkResult": "1",
  "userId": "复核人ID",
  "userName": "复核人姓名"
}
```

## 错误代码说明

### 通用响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 常见错误信息
- **权限异常**: 用户无权限访问该功能
- **权限不足**: 用户权限级别不满足操作要求
- **本任务附件未上传,不能操作**: 任务完成时缺少必需附件
- **本任务依赖前置任务还未完成,不能操作**: 存在未完成的依赖任务
- **不允许批量完成**: 该任务不支持批量操作

## 使用示例

### 创建临时任务并设置提醒
```http
POST /taskCenter/createByTemp
Content-Type: application/json

{
  "taskName": "月报整理",
  "taskDesc": "整理本月工作月报",
  "taskEndTime": "2024-01-31 18:00:00",
  "remind": true,
  "tipDate": "2024-01-30",
  "tipMessage": "明天需要提交月报",
  "tipType": 1
}
```

### 查询待办任务
```http
GET /taskCenter/todoList?date=2024-01-15
```

### 完成任务
```http
POST /taskCenter/taskComplete
Content-Type: application/json

{
  "taskId": "123456789",
  "taskDesc": "已完成月报整理，共20页",
  "workAmount": 1
}
```

### 复核任务
```http
POST /taskCenter/taskAudit
Content-Type: application/json

{
  "taskId": "123456789",
  "taskDesc": "月报内容完整，格式规范，复核通过",
  "checkResult": "1"
}
```

## 注意事项

1. **时间格式**: 所有时间字段使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **权限检查**: 大部分操作都会进行权限验证，确保用户有相应操作权限
3. **事务处理**: 涉及多表操作的接口支持事务回滚
4. **批量操作**: 支持批量转派、批量复核、批量未发生等操作
5. **父子任务**: 删除父任务时会自动删除所有子任务
6. **状态同步**: 任务状态变更可能会影响父子任务的状态同步