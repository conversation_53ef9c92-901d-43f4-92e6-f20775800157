--- # server 配置
server:
  port: 9100
  servlet:
    context-path: /xxl-job-admin
spring:
  application:
    name: ruoyi-xxl-job-admin
  profiles:
    active: @profiles.active@
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
  web:
    resources:
      static-locations: classpath:/static/

logging:
  config: classpath:logback-plus.xml

--- # mybatis 配置
mybatis:
  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml

--- # 页面配置
spring:
  freemarker:
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########
    suffix: .ftl
    templateLoaderPath: classpath:/templates/

--- # Actuator 监控端点的配置项
management:
  health:
    mail:
      enabled: false
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/ruoyi-xxl-job-admin.log

--- # xxljob系统配置
xxl:
  job:
    # 鉴权token
    accessToken: xxl-job
    # 国际化
    i18n: zh_CN
    # 日志清理
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
