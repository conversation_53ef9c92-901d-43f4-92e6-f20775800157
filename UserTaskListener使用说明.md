# UserTaskListener 任务模板创建功能使用说明

## 概述

UserTaskListener 是一个 Flowable 任务监听器，可以在流程执行过程中自动通过任务模板创建任务。该监听器支持多种方式获取模板ID，并能够根据模板配置自动创建相应的任务实例。

## 功能特性

### 1. 多种模板ID获取方式
- **注入字段获取**: 通过流程设计时配置的 `templateId` 字段
- **流程变量获取**: 从流程执行过程中的变量获取
- **任务定义推导**: 从任务定义Key中解析模板ID

### 2. 完整的模板验证
- 检查模板是否存在
- 验证模板状态（是否已作废）
- 确认模板下是否有任务副本

### 3. 灵活的调度支持
- **Manual模式**: 立即创建任务实例
- **Daily模式**: 创建定时任务关联关系
- **Dynamic模式**: 支持自定义触发规则

### 4. 完善的日志记录
- 详细的执行日志
- 错误处理和异常记录
- 流程变量记录

## 在 Flowable 流程中的配置

### 1. 在流程设计器中配置监听器

#### 方式一：通过 XML 配置
```xml
<userTask id="userTask1" name="用户任务">
  <extensionElements>
    <flowable:taskListener 
        event="create" 
        delegateExpression="${userTaskListener}">
      <flowable:field name="templateId">
        <flowable:string>TEMPLATE_001</flowable:string>
      </flowable:field>
      <flowable:field name="taskDescription">
        <flowable:string>通过模板创建任务</flowable:string>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

#### 方式二：通过流程设计器界面配置
1. 选择用户任务节点
2. 在"监听器"选项卡中添加任务监听器
3. 设置以下参数：
   - **事件**: `create`
   - **类型**: `委托表达式`
   - **委托表达式**: `${userTaskListener}`
   - **字段注入**:
     - `templateId`: 模板ID值
     - `taskDescription`: 任务描述

### 2. 流程变量方式配置

在流程启动或执行过程中设置流程变量：

```java
// 启动流程时设置变量
Map<String, Object> variables = new HashMap<>();
variables.put("templateId", "TEMPLATE_001");
variables.put("taskTemplateId", "TEMPLATE_002");  // 备选变量名
runtimeService.startProcessInstanceByKey("processKey", variables);

// 流程执行中设置变量
runtimeService.setVariable(processInstanceId, "templateId", "TEMPLATE_003");
```

### 3. 任务定义Key推导方式

在设计流程时，将任务定义Key按照约定格式命名：

```xml
<userTask id="userTask_template_123" name="模板任务">
  <!-- 监听器配置 -->
</userTask>
```

监听器会自动从 `userTask_template_123` 中提取模板ID `123`。

## 使用示例

### 示例1：审批流程中创建后续任务

```xml
<process id="approvalProcess" name="审批流程">
  <startEvent id="start"/>
  
  <userTask id="approvalTask" name="审批任务">
    <extensionElements>
      <flowable:taskListener event="complete" delegateExpression="${userTaskListener}">
        <flowable:field name="templateId">
          <flowable:expression>${approvalTemplateId}</flowable:expression>
        </flowable:field>
      </flowable:taskListener>
    </extensionElements>
  </userTask>
  
  <endEvent id="end"/>
</process>
```

### 示例2：根据审批结果创建不同模板任务

```java
// 在审批完成后设置不同的模板ID
if ("approved".equals(approvalResult)) {
    taskService.setVariable(taskId, "templateId", "APPROVED_TEMPLATE");
} else {
    taskService.setVariable(taskId, "templateId", "REJECTED_TEMPLATE");
}
```

### 示例3：动态模板选择

```xml
<userTask id="dynamicTask" name="动态任务">
  <extensionElements>
    <flowable:taskListener event="create" delegateExpression="${userTaskListener}">
      <flowable:field name="templateId">
        <flowable:expression>${processType == 'urgent' ? 'URGENT_TEMPLATE' : 'NORMAL_TEMPLATE'}</flowable:expression>
      </flowable:field>
    </flowable:taskListener>
  </extensionElements>
</userTask>
```

## 监听器执行流程

### 1. 触发时机
- **create**: 任务创建时触发
- **assignment**: 任务分配时触发
- **complete**: 任务完成时触发
- **delete**: 任务删除时触发

### 2. 执行步骤
1. **获取模板ID**
   - 尝试从注入字段获取
   - 尝试从流程变量获取（支持多种变量名）
   - 尝试从任务定义Key推导

2. **验证模板**
   - 检查模板是否存在
   - 验证模板状态是否有效
   - 确认是否有任务副本

3. **创建任务**
   - 构建 TemplateVO 对象
   - 调用任务创建服务
   - 记录执行结果到流程变量

4. **日志记录**
   - 记录详细的执行信息
   - 记录错误和异常
   - 更新流程变量状态

## 错误处理机制

### 1. 模板不存在
```
ERROR - 任务模板不存在，模板ID: TEMPLATE_001
```
- 记录错误日志
- 继续流程执行（不中断）

### 2. 模板已作废
```
ERROR - 任务模板已作废，模板ID: TEMPLATE_001, 状态: 1
```
- 记录错误日志
- 跳过任务创建

### 3. 模板无任务副本
```
WARN - 模板下没有任务副本，模板ID: TEMPLATE_001
```
- 记录警告日志
- 跳过任务创建

### 4. 创建过程异常
```
ERROR - 通过模板创建任务失败，模板ID: TEMPLATE_001
```
- 记录详细异常信息
- 可配置是否中断流程

## 流程变量记录

监听器执行成功后会设置以下流程变量：

```java
// 记录任务创建状态
delegateTask.setVariable("taskCreatedFromTemplate", true);
// 记录使用的模板ID
delegateTask.setVariable("templateIdUsed", "TEMPLATE_001");
// 记录模板名称
delegateTask.setVariable("templateName", "审批后续任务模板");
```

## 配置建议

### 1. 模板设计建议
- **模块化设计**: 将复杂业务拆分为多个模板
- **命名规范**: 使用清晰的模板命名约定
- **状态管理**: 及时维护模板的有效状态

### 2. 流程设计建议
- **监听器位置**: 根据业务需求选择合适的触发事件
- **变量命名**: 使用统一的变量命名规范
- **错误处理**: 设计合适的异常处理策略

### 3. 性能优化建议
- **模板缓存**: 对常用模板进行缓存
- **批量操作**: 避免频繁的单个任务创建
- **异步处理**: 对于复杂模板可考虑异步创建

## 监控和调试

### 1. 日志监控
```properties
# 启用详细日志
logging.level.com.ruoyi.listener.UserTaskListener=DEBUG
```

### 2. 流程变量检查
```java
// 检查任务创建状态
Boolean taskCreated = (Boolean) runtimeService.getVariable(processInstanceId, "taskCreatedFromTemplate");
String templateUsed = (String) runtimeService.getVariable(processInstanceId, "templateIdUsed");
```

### 3. 常见问题排查
- **模板ID获取失败**: 检查字段注入和流程变量设置
- **权限问题**: 确认当前用户有模板访问权限
- **依赖注入问题**: 检查Service依赖是否正确注入

## 扩展开发

### 1. 自定义模板ID获取逻辑
可以在 `deriveTemplateIdFromTask` 方法中添加自定义的模板ID推导规则：

```java
private String deriveTemplateIdFromTask(DelegateTask delegateTask) {
    // 自定义业务规则
    String processKey = delegateTask.getProcessDefinitionId();
    if (processKey.contains("approval")) {
        return "APPROVAL_TEMPLATE";
    }
    // 其他推导逻辑...
    return null;
}
```

### 2. 添加自定义验证逻辑
在 `createTaskFromTemplate` 方法中添加业务相关的验证：

```java
// 添加业务验证
if (!isUserAuthorizedForTemplate(templateIdValue)) {
    log.error("用户无权限使用模板: {}", templateIdValue);
    return;
}
```

### 3. 集成其他系统
可以扩展监听器与其他系统集成：

```java
// 发送通知
notificationService.sendTaskCreatedNotification(templateIdValue);

// 记录审计日志
auditService.recordTemplateUsage(templateIdValue, delegateTask.getProcessInstanceId());
```

这个监听器为 Flowable 流程提供了强大的任务模板创建能力，可以根据具体业务需求进行配置和扩展。