package com.ruoyi.web.taskcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.taskcenter.domain.OpsTaskJobRelation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【ops_task_job_relation】的数据库操作Mapper
* @createDate 2024-07-05 15:39:29
* @Entity generator.domain.OpsTaskJobRelation
*/
@Mapper
public interface OpsTaskJobRelationMapper extends BaseMapper<OpsTaskJobRelation> {

    @Delete("delete from OPS_TASK_JOB_RELATION  WHERE task_id in (" +
            "   select task_replica_id from OPS_TASK_TEMPLATE_RELATION where template_id =#{id})")
    void deleteTaskReplicaIdsByTemplateId(String id);
}




