//package com.ruoyi.web.taskcenter.domain;
//
//import com.baomidou.mybatisplus.annotation.*;
//import com.fasterxml.jackson.annotation.JsonFormat;
//import lombok.Data;
//import org.springframework.format.annotation.DateTimeFormat;
//
//import java.util.Date;
//
//@Data
//public class BaseEntity {
//
//    /**
//     * 数据标识
//     */
//    @TableId(value = "id", type = IdType.ASSIGN_ID)
//    private String id;
//
//    /**
//     * 创建人
//     */
//    @TableField(fill = FieldFill.INSERT)
//    private String createBy;
//
//    /**
//     * 创建时间
//     */
//    @TableField(fill = FieldFill.INSERT)
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date createTime;
//
//    /**
//     * 更新人
//     */
//    @TableField(fill = FieldFill.INSERT_UPDATE)
//    private String updateBy;
//
//    /**
//     * 更新时间
//     */
//    @TableField(fill = FieldFill.INSERT_UPDATE)
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date updateTime;
//
//    /**
//     * 逻辑删除标识
//     */
//    @TableLogic
//    private Integer deleted;
//}
