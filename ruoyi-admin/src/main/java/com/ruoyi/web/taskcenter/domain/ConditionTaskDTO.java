package com.ruoyi.web.taskcenter.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ConditionTaskDTO implements Serializable {

    private String userId;

    private List<String> postIds;

    private String postId;

    /**
     * type  查询sql组装
     *   1    用户看类型为个人和类型为岗位的
     *   2    用户查看所有自己岗位的或者指定岗位
     *   4    管理员
     */
    private Integer type;

}
