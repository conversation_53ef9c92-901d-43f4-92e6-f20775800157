package com.ruoyi.web.taskcenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.web.taskcenter.domain.OpsTaskTemplateRelation;
import com.ruoyi.web.taskcenter.mapper.OpsTaskTemplateRelationMapper;
import com.ruoyi.web.taskcenter.service.OpsTaskTemplateRelationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_template_relation(菜单权限表)】的数据库操作Service实现
 * @createDate 2024-07-10 20:11:30
 */
@Service
public class OpsTaskTemplateRelationServiceImpl extends ServiceImpl<OpsTaskTemplateRelationMapper, OpsTaskTemplateRelation>
        implements OpsTaskTemplateRelationService {

    @Override
    public List<String> findIds(String templateId) {
        return baseMapper.findReplicaIds(templateId);
    }
}




