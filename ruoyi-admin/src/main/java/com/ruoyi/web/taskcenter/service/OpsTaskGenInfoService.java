package com.ruoyi.web.taskcenter.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.web.taskcenter.domain.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_gen_info】的数据库操作Service
 * @createDate 2024-07-04 10:28:39
 */
public interface OpsTaskGenInfoService extends IService<OpsTaskGenInfo> {


    void createSingleTask(OpsTaskGenInfo info);

    void createSingleTask(OpsTaskGenInfo info, String taskId);


    void schedulerProcessByDaily();

    void replaceIdAndFillChildIdsAndSort(List<OpsTaskGenInfo> save);

    void schedulerProcessByDynamic(Long taskId, String taskType);

    Map<Long, List<OpsTaskGenInfo>> findChild(Map<Long, String> longs, ConditionTaskDTO condition);


    List<OpsTaskGenInfo> detailForDashboard(Date nowTime, String genTime, ConditionTaskDTO condition);

    List<OpsTaskGenInfo> detailStaticForDashboard(Date nowTime, String genTime, ConditionTaskDTO condition);

    Map<Long, List<OpsTaskGenInfo>> findChildByLeader(Map<Long, String> longs, ConditionTaskDTO condition);

    Map<Long, List<OpsTaskGenInfo>> findChildForAudit(Map<Long, String> p_child);

    void createTaskByTemplate(TemplateVO vo, Boolean up);

    IPage<OpsTaskGenInfo> pageCustom(IPage<OpsTaskGenInfo> page, Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition);

    List<OpsTaskGenInfo> dashboardListTask(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition);

    /**
     * 统计指标详情列表相关
     *
     * @param wrapper
     * @param condition
     * @return
     */
    List<OpsTaskGenInfo> dashboardListTaskDetail(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition);

    String taskComplete(TaskCompleteVO taskId);

    List<Long> dashboardListTaskCount(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition);

    IPage<OpsTaskGenInfo> pageCustomAudit(IPage<OpsTaskGenInfo> pageEntity, LambdaQueryWrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition);

    List<OpsTaskGenInfo> listCustomAudit(LambdaQueryWrapper<OpsTaskGenInfo> wrapper,
                                         ConditionTaskDTO condition);

    Map<Long, List<OpsTaskGenInfo>> findChildByAudit(Map<Long, String> pChild, ConditionTaskDTO condition);

    void transferTask(TaskTransferVO transferVO);

    void batchTransferTask(TaskTransferVO transferVO);

    void taskDescUpdate(TaskCompleteVO vo);

    void taskNoExist(TaskCompleteVO vo);

    void multiTaskNoExist(TaskCompleteVO vo);

    OpsTaskGenInfo convertGenInfo(OpsTaskAttrBasic opsTaskAttrBasic);

    OpsTaskGenInfo convertGenInfoByTemp(OpsTaskAttrBasic opsTaskAttrBasic);

    void reviewTaskProcess(ConditionTaskDTO condition, TaskCheckVO transferVO);

    ConditionTaskDTO taskSpecialAuthFilter(String orgId);


    void timerEverDayScannerDelayTaskStatus();

    List<OpsTaskGenInfo> multiTaskListForDashboard(Date nowTime, String genTime, ConditionTaskDTO condition);



    List<OpsTaskGenInfo> multiTaskListForDashboardLeaf(Date nowTime, String genTime, ConditionTaskDTO condition);

    List<OpsTaskGenInfo> multiTaskListForDashboardLeafByLeader(Date nowTime, String genTime, ConditionTaskDTO condition);

    /**
     * 统计指标详情列表相关
     *
     * @param nowTime
     * @param genTime
     * @param condition
     * @return
     */
    List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetail(Date nowTime, String genTime, ConditionTaskDTO condition);

    List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailPro(Date nowTime,Date fullTimeSt,Date fullTimeEt,  String genTime, ConditionTaskDTO condition);


    List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailProForCount(Date nowTime, Date fullTimeSt, Date fullTimeEt, String genTime, ConditionTaskDTO condition);

    List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailByLeader(Date nowTime, String genTime, ConditionTaskDTO condition);


    List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailByLeaderForCount(Date nowTime, String genTime, ConditionTaskDTO condition);

    OpsTaskGenInfo convertGenInfo(OpsTaskAttrBasicReplica OpsTaskAttrBasicReplica);

    void realDeleted(String format);

    void batchReviewTaskProcess(ConditionTaskDTO condition, TaskCheckVO checkVO);

    void taskReset(String id);

    List<OpsTaskGenInfo> allTaskForNowDay(String date);

    List<OpsTaskGenInfo> allTaskForNowDayV1(String date);

    List<OpsTaskGenInfo> checkTaskForUser(String date, String userId);

    /**
     * 统计指标详情列表相关
     *
     * @param date
     * @param userId
     * @return
     */
    List<OpsTaskGenInfo> checkTaskForUserDetail(String date, String userId);

    List<OpsTaskGenInfo> checkTaskListForLeaderOrUser(String genTime, ConditionTaskDTO condition);

    List<OpsTaskGenInfo> checkTaskListForLeader(String genTime, ConditionTaskDTO condition);

    /**
     * 统计指标详情列表相关
     *
     * @param genTime
     * @param condition
     * @return
     */
    List<OpsTaskGenInfo> checkTaskListForLeaderOrUserDetail(String genTime, ConditionTaskDTO condition);

    /**
     * 通过子节点id获取父节点数据
     *
     * @param ids
     * @return
     */
    List<OpsTaskGenInfo> getParentListByChildIds(List<Long> ids);

    List<OpsTaskGenInfo> dashboardTempListTaskLeaf(Wrapper<OpsTaskGenInfo> opsTaskGenInfoWrapper, ConditionTaskDTO condition);

    List<OpsTaskGenInfo> listByTemplateIdAndNowDate(String templateId, String format);

    List<OpsTaskGenInfo> multiTaskListForDashboardByLeader(Date now, String genTime, ConditionTaskDTO condition);

    void todoListExport(List<OpsTaskGenInfo> list,String startDate,String endDate, HttpServletResponse httpServletResponse,String orgName);

    List<WorkAmountByOrgVO> getWorkAmountByYearAndMonth(String year,String month) throws CloneNotSupportedException;

    List<OpsTaskGenInfo> getAmountTaskByUser(String year,String month,String userId,String orgId);

    List<OpsTaskGenInfo> getYearAmountTaskByUser(String year,String userId);

    void removeInvalidTasks();
}
