package com.ruoyi.web.taskcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.web.taskcenter.domain.OpsSpecThirdInfo;
import com.ruoyi.web.taskcenter.domain.OpsTaskAttrBasicReplica;
import com.ruoyi.web.taskcenter.util.TaskException;


import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_attr_basic_replica】的数据库操作Service
 * @createDate 2024-07-10 20:11:30
 */
public interface OpsTaskAttrBasicReplicaService extends IService<OpsTaskAttrBasicReplica> {

    List<OpsTaskAttrBasicReplica> viewList(String id);


    List<OpsTaskAttrBasicReplica> queryListByTemplateId(String templateId);


    List<OpsTaskAttrBasicReplica> queryListByTemplateIdOfCreateType1();

    List<OpsSpecThirdInfo> queryUnProcessDataByUserId(String userId);

    void modifySpecTaskInfo(String userId, String dataId, String taskId, String triggerId) throws TaskException;

    List<OpsTaskAttrBasicReplica> queryReplicaHaveImportListByTemplateId(List<String> templateIdList);

    List<OpsTaskAttrBasicReplica> queryReplicaListByTemplateIdAndUserIdWithOwner(List<String> templateIds, String fromId);

    /**
     * 通过ids获取本身以及父信息列表
     *
     * @param ids
     * @return
     */
    List<OpsTaskAttrBasicReplica> queryParentAndSelfListBySelfIds(List<String> ids);
}
