package com.ruoyi.web.taskcenter.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.web.taskcenter.domain.ConditionTaskDTO;
import com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo;
import com.ruoyi.web.taskcenter.domain.OpsTaskReminder;
import com.ruoyi.web.taskcenter.service.OpsTaskGenInfoService;
import com.ruoyi.web.taskcenter.service.OpsTaskReminderService;
import com.ruoyi.web.taskcenter.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequestMapping("/system/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    private final OpsTaskGenInfoService opsTaskGenInfoService;

  //  private final OpsTaskFundInfoService opsTaskFundInfoService;

    private final OpsTaskReminderService opsTaskReminderService;

    /**
     * 获取与用户相关的任务列，分页形式
     *
     * @return IPage
     */
    @GetMapping("/list")
    public R<Object> taskList(@RequestParam(value = "orgId", required = false) String orgId,
                              @RequestParam(value = "date", required = false) String dateTime) {
        long startTime, endTime;

        // 附加权限查询生成
        startTime = System.currentTimeMillis();
        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(orgId);
        endTime = System.currentTimeMillis();
        log.info("opsTaskGenInfoService.taskSpecialAuthFilter 耗时: " + (endTime - startTime) + " 毫秒");

        // 如果条件附加为 null 则组织权限未配置
        if (Objects.isNull(condition)) {
            return R.data(new ArrayList<>());
        }

        // 第一次查询都为根节点，即 pid = 0 的
        // 主日常任务查询，每个交易日的
        // 特殊任务 - 延期的
        // add  延期的会把完成时间调整为顺延工作日的最大值，比如当天是 2024 - 07 - 21 17:00 可以延期 3 天 则是 2024 - 07 - 24 17:00 查询此类数据
        // 特殊任务 - 周期轮转的
        // add 周期任务举例，也是每日要生成的，但是开始干这个任务可以是 T 日，也可以是结束日之前 比如结束日是 T + 5 ，那么开始日期就可以是 T + 5 以内任何一个日期

        String genTime = !StringUtils.hasText(dateTime) ? DateUtil.format(new Date(), "yyyy-MM-dd") : dateTime;
        Date now = !StringUtils.hasText(dateTime) ? new Date() : DateUtil.parse(dateTime + " " + DateUtil.format(new Date(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");

        startTime = System.currentTimeMillis();
        List<OpsTaskGenInfo> resc = opsTaskGenInfoService.multiTaskListForDashboard(now, genTime, condition);
        endTime = System.currentTimeMillis();
        log.info("opsTaskGenInfoService.multiTaskListForDashboard 耗时: " + (endTime - startTime) + " 毫秒");

        // 排序
        resc.sort(Comparator.comparing(OpsTaskGenInfo::getTaskSort, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(OpsTaskGenInfo::getTaskBindTemplateId, Comparator.nullsLast(Comparator.naturalOrder())));

        // 查询该节点内所有有子集节点任务清单，并树状组织,赋值给当前分页数据
        Map<Long, String> p_child = new HashMap<>();
        for (OpsTaskGenInfo info : resc) {
            if (StringUtils.hasText(info.getTaskChildIds())) {
                p_child.put(info.getId(), info.getTaskChildIds());
            }
        }

        if (!p_child.isEmpty()) {
            startTime = System.currentTimeMillis();
            // 查询当前任务中有子任务的清单项目，并重新赋值
            Map<Long, List<OpsTaskGenInfo>> child = opsTaskGenInfoService.findChild(p_child, condition);
            endTime = System.currentTimeMillis();
            log.info("opsTaskGenInfoService.findChild 耗时: " + (endTime - startTime) + " 毫秒");

            for (OpsTaskGenInfo record : resc) {
                if (child.containsKey(record.getId())) {
                    record.setChildren(child.get(record.getId()));
                }
            }
        }

        List<OpsTaskGenInfo> opsTaskGenInfoFlatList = resc.stream()
            .flatMap(DashboardController::flattenNodeStream)
            .collect(Collectors.toList());

        if (!opsTaskGenInfoFlatList.isEmpty()) {
            startTime = System.currentTimeMillis();
            List<OpsTaskReminder> opsTaskReminderList = opsTaskReminderService.list(new LambdaQueryWrapper<OpsTaskReminder>().in(OpsTaskReminder::getTaskId, opsTaskGenInfoFlatList.stream().map(OpsTaskGenInfo::getId).collect(Collectors.toList())));
            endTime = System.currentTimeMillis();
            log.info("opsTaskReminderService.list 耗时: " + (endTime - startTime) + " 毫秒");

            Map<Long, OpsTaskGenInfo> taskIdToOpsTaskGenInfoMap = opsTaskGenInfoFlatList.stream()
                .collect(Collectors.toMap(OpsTaskGenInfo::getId, e -> e));

            for (OpsTaskReminder opsTaskReminderItem : opsTaskReminderList) {
                OpsTaskGenInfo opsTaskGenInfo = taskIdToOpsTaskGenInfoMap.get(opsTaskReminderItem.getTaskId());
                if (opsTaskGenInfo != null) {
                    opsTaskGenInfo.setReminder(1);
                }
            }
        }

        startTime = System.currentTimeMillis();
        // 查询模板关联产品的任务
       // List<OpsTaskGenInfo> opsTaskInfoList = opsTaskFundInfoService.getOpsTaskInfoList(orgId, genTime);
        endTime = System.currentTimeMillis();
        log.info("opsTaskFundInfoService.getOpsTaskInfoList 耗时: " + (endTime - startTime) + " 毫秒");

       // resc.addAll(opsTaskInfoList);

        return R.data(resc);
    }

    private static Stream<OpsTaskGenInfo> flattenNodeStream(OpsTaskGenInfo node) {
        if (node.getChildren() == null || node.getChildren().isEmpty()) {
            return Stream.of(node);
        }
        return Stream.concat(
            Stream.of(node),
            node.getChildren().stream().flatMap(DashboardController::flattenNodeStream)
        );
    }
}
