package com.ruoyi.web.taskcenter.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.shift.service.OpsSysCalendarService;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.taskcenter.domain.*;
import com.ruoyi.web.taskcenter.mapper.OpsTaskGenInfoMapper;
import com.ruoyi.web.taskcenter.service.*;
import com.ruoyi.web.taskcenter.util.CommonConstant;
import com.ruoyi.web.taskcenter.util.SecureUtil;
import com.ruoyi.web.taskcenter.util.TaskConstant;
import com.ruoyi.web.taskcenter.util.TaskException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_gen_info】的数据库操作Service实现
 * @createDate 2024-07-04 10:28:39
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpsTaskGenInfoServiceImpl extends ServiceImpl<OpsTaskGenInfoMapper, OpsTaskGenInfo>
        implements OpsTaskGenInfoService {

    // 延迟转派服务类
    private final OpsTaskTransferConfService opsTaskTransferConfService;
    //配置服务来源服务类
    private final OpsTaskAttrBasicService opsTaskAttrBasicService;
    //任务配置单元与job关联id服务类
    private final OpsTaskJobRelationService relationService;
    //定时任务处理器
    //任务模板服务
    private final OpsTaskTemplateService taskTemplateService;
    //任务模板中任务单位副本服务
    private final OpsTaskAttrBasicReplicaService replicaService;
    //任务文件上传列表
    private final OpsTaskGenInfoFileService opsTaskGenInfoFileService;

//    //任务特殊规则日志项
//    private final OpsTradeTypeService tradeTypeService;
    //组织管理
    private final ISysDeptService opsSysOrgService;

    private final ISysUserService iSysUserService;

    private final SysDeptMapper sysDeptMapper;
    //交易日
    private final OpsSysCalendarService sysCalendarService;


    private final OpsTaskGenInfoAsyncService asyncService;

    private final OpsTaskReminderService opsTaskReminderService;

    private final OpsTaskMessageTipService opsTaskMessageTipService;

    private final OpsTaskHandledRecordService opsTaskHandledRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSingleTask(OpsTaskGenInfo info) {
        //查看是否是自动生成的任务，如果是定时生成需要配置生成任务，如果为手动则立即生成
        //立即插入
        info.setId(null);
        save(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSingleTask(OpsTaskGenInfo info, String taskId) {
        //查看是否是自动生成的任务，如果是定时生成需要配置生成任务，如果为手动则立即生成
//        if (Objects.equals(TaskConstant.MANUAL, info.getTaskTriggerType())) {
//            //立即插入
//            info.setId(null);
//            save(info);
//            updateTaskBasicStatus(taskId);
//        }
//        if (!Objects.equals(TaskConstant.MANUAL, info.getTaskTriggerType())) {
//            //创建一个定时任务，到时间插入
//            saveSchedulerPlanByUnit(info, taskId);
//            updateTaskBasicStatus(taskId);
//        }
//        //查看是否是自动完成的任务
//        if (Objects.equals(TaskConstant.AUTO, info.getTaskCompleteType())) {
//            //自动完成需要存储脚本执行id，做任务关联，并创建一个调度任务
//            //todo
//        }
//        //查看是否需要自动稽核
//        //如果需要自动稽核
//        if (Objects.equals(info.getTaskAuditType(), "1")) {
//            //自动稽核绑定一个脚本执行id和任务关联，并创建一个调度任务
//            //todo
//        }
        info.setId(null);
        save(info);
        //查看是否需要告警,告警为统一定时任务轮询查询执行
    }

    @Override
    public void schedulerProcessByDaily() {

    }

    /**
     * 更新任务单元状态，生效/上线
     *
     * @param taskId 任务单元id
     */
    private void updateTaskBasicStatus(String taskId) {
        OpsTaskAttrBasic up = new OpsTaskAttrBasic();
        up.setTaskStatus(1);
        up.setId(Long.valueOf(taskId));
        opsTaskAttrBasicService.updateById(up);
    }

    /**
     * 日常任务调度入口，包含单条任务日常生成与模板任务日常生成，模板任务占比90以上
     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void schedulerProcessByDaily() {
//        if (!opsTradeTypeService.checkWorkdayByToday()) {
//            log.info("日常任务-执行失败,当天不是工作日");
//            return;
//        }
//        List<OpsTaskJobRelation> relations = relationService.list();
//        dailySingleTask(relations);
//        dailyTemplateTask(relations);
//    }

//    /**
//     * 日常单条任务定时调度配置
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void dailySingleTask(List<OpsTaskJobRelation> relations) {
//        List<String> taskIds = relations.stream().filter(i -> i.getTaskType().equals("1")).map(OpsTaskJobRelation::getTaskId).collect(Collectors.toList());
//        if (taskIds.isEmpty()) {
//            log.info("daily-single-scheduler未发现日常任务需要执行");
//            return;
//        }
//        LambdaQueryWrapper<OpsTaskAttrBasic> basics = new LambdaQueryWrapper<>();
//        basics.in(OpsTaskAttrBasic::getId, taskIds);
//        List<OpsTaskAttrBasic> arr = opsTaskAttrBasicService.list(basics);
//        List<OpsTaskGenInfo> save = arr.stream().map(this::convertGenInfo).collect(Collectors.toList());
//        saveBatch(save);
//        log.info("daily-single-scheduler生成任务清单数量{}", save.size());
//    }

//    /**
//     * 模板任务生成
//     *
//     * @param relations 关系表
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void dailyTemplateTask(List<OpsTaskJobRelation> relations) {
//        List<String> taskIds = relations.stream().filter(i -> i.getTaskType().equals("2")).map(OpsTaskJobRelation::getTaskId).collect(Collectors.toList());
//        if (taskIds.isEmpty()) {
//            log.info("daily-template-scheduler未发现日常任务需要执行");
//            return;
//        }
//        LambdaQueryWrapper<OpsTaskAttrBasicReplica> basics = new LambdaQueryWrapper<>();
//        basics.in(OpsTaskAttrBasicReplica::getId, taskIds);
//        List<OpsTaskAttrBasicReplica> arr = replicaService.list(basics);
//        List<OpsTaskGenInfo> save = arr.stream().map(this::convertGenInfo).collect(Collectors.toList());
//        replaceIdAndFillChildIdsAndSort(save);
//        saveBatch(save);
//        log.info("daily-template-scheduler生成任务清单数量{}", save.size());
//    }

    /**
     * 对模板引用的replica 内容首先进行id替换，插入清单表使用
     * 然后，排序根节点在前
     * 最后根节点要存储所有子级的id集合
     *
     * @param
     */
    @Override
    public void replaceIdAndFillChildIdsAndSort(List<OpsTaskGenInfo> save) {
        //生成新的清单，但是要保留 id-pid 的关系
        Map<Long, Long> idMapper = new HashMap<>();
        for (OpsTaskGenInfo info : save) {
            Long newId = IdWorker.getId();
            idMapper.put(info.getId(), newId);
            info.setId(newId);
        }
        for (OpsTaskGenInfo info : save) {
            if (info.getParentId() != 0L) {
                info.setParentId(idMapper.get(info.getParentId()) == null ? 0L : idMapper.get(info.getParentId()));
            }
            //如果有依赖,id也需要替换
            if (StringUtils.hasText(info.getDependOnIds())) {
                info.setDependOnIds(replaceOldId(info.getDependOnIds(), idMapper));
            }
        }
        //替换完成，查找根节点所有子节点集合
        //先排序将pid=0的在前
        save.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        getGroupedDescendantIds(save);
    }

    @Override
    public void schedulerProcessByDynamic(Long taskId, String taskType) {

    }

    private String replaceOldId(String dependOnIds, Map<Long, Long> idMapper) {
        StringJoiner joiner = new StringJoiner(",");
        for (String oldId : dependOnIds.split(",")) {
            joiner.add(idMapper.get(Long.parseLong(oldId)) + "");
        }
        return joiner.toString();
    }


    // 方法用于获取所有顶级节点（pid=0）及其子孙节点的ID，并按顶级节点分组
    public static void getGroupedDescendantIds(List<OpsTaskGenInfo> tasks) {
        for (OpsTaskGenInfo task : tasks) {
            List<Long> childDes = getAllDescendantIds(task, tasks);
            if (!childDes.isEmpty()) {
                StringJoiner joiner = new StringJoiner(",");
                for (Long l : childDes) {
                    joiner.add(l + "");
                }
                task.setTaskChildIds(joiner.toString());
            }
        }
    }

    static List<Long> getAllDescendantIds(OpsTaskGenInfo info, List<OpsTaskGenInfo> tasks) {
        List<Long> ids = new ArrayList<>();
        for (OpsTaskGenInfo child : tasks) {
            if (child.getParentId().equals(info.getId())) {
                ids.add(child.getId());
                ids.addAll(getAllDescendantIds(child, tasks));
            }
        }
        return ids;
    }


//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void schedulerProcessByDynamic(Long taskId, String taskType) {
//        //自定义执行的任务考虑,重复周期,触发器只管采集结果，所以任务要去判定是否在允许重复的周期内已经执行过一次了，当前场景默认当日
//        if (dynamicLogService.checkTaskIsRecord(taskId)) {
//            log.error("日常任务-执行失败,该任务已经在当天执行过");
//            return;
//        }
//        //单个任务单元生成任务
//        if (Objects.equals(taskType, "1")) {
//            OpsTaskAttrBasic basic = opsTaskAttrBasicService.getById(taskId);
//            if (Objects.isNull(basic)) {
//                log.error("dynamic-scheduler任务执行失败,未找到配置基类{}", taskId);
//                return;
//            }
//            if (!StringUtils.hasText(basic.getTaskTriggerId())) {
//                log.error("dynamic-scheduler任务执行失败,未找到触发器配置");
//                return;
//            }
//            TaskScriptResultVO ruleEnd = tradeTypeService.checkDateType(basic.getTaskTriggerId());
//            if (!ruleEnd.getStatus()) {
//                log.error("dynamic-scheduler任务生成异常,触发器规则执行结果为false 任务单元id={}", basic.getId());
//            }
//            //如果不为false,则需要根据任务类型判定是否已经生成过该任务
//            OpsTaskGenInfo info = convertGenInfo(basic);
//            save(info);
//            dynamicLogService.insertSuccessTaskRecord(taskId);
//            log.info("dynamic-scheduler 自定义-通过任务单元-生成任务清单完成{}", taskId);
//        }
//        //通过模板生成任务
//        if (Objects.equals(taskType, "2")) {
//            OpsTaskTemplate template = taskTemplateService.getById(taskId);
//            if (Objects.isNull(template)) {
//                log.error("dynamic-scheduler任务执行失败,未找到配置基类{}", taskId);
//                return;
//            }
//            if (!StringUtils.hasText(template.getTriggerId())) {
//                log.error("dynamic-scheduler任务执行失败,未找到触发器配置");
//                return;
//            }
//            TaskScriptResultVO ruleEnd = tradeTypeService.checkDateType(template.getTriggerId());
//            if (!ruleEnd.getStatus()) {
//                log.error("dynamic-scheduler任务生成异常,触发器规则执行结果为false 模板id={}", template.getId());
//            }
//            List<String> ids = taskTemplateService.listReplicaIds(taskId);
//            LambdaQueryWrapper<OpsTaskAttrBasicReplica> basics = new LambdaQueryWrapper<>();
//            basics.in(OpsTaskAttrBasicReplica::getId, ids);
//            List<OpsTaskAttrBasicReplica> arr = replicaService.list(basics);
//            List<OpsTaskGenInfo> save = arr.stream().map(this::convertGenInfo).collect(Collectors.toList());
//            replaceIdAndFillChildIdsAndSort(save);
//            saveBatch(save);
//            dynamicLogService.insertSuccessTaskRecord(taskId);
//            log.info("dynamic-template-scheduler生成任务清单数量{}", save.size());
//        }
//    }

    /**
     * 任务清单查询，分页情况下，最顶层是根节点，有子级节点需要二次查询并写入
     *
     * @param p_child
     * @param condition
     * @return
     */
    @Override
    public Map<Long, List<OpsTaskGenInfo>> findChild(Map<Long, String> p_child, ConditionTaskDTO condition) {
        List<String> longs = new ArrayList<>();
        //取所有子节点id
        p_child.forEach((k, v) -> {
            longs.addAll(List.of(v.split(",")));
        });
        //查询数据
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OpsTaskGenInfo::getId, longs);
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        List<OpsTaskGenInfo> childMeta = baseMapper.findChildDetailByAuth(queryWrapper, condition);
        //取id与实体对象做映射
        Map<Long, OpsTaskGenInfo> nodeMap = new HashMap<>();
        for (OpsTaskGenInfo node : childMeta) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }
        //进行树状生成
        childMeta.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        Map<Long, List<OpsTaskGenInfo>> res = new HashMap<>();
        p_child.forEach((k, v) -> {
            List<OpsTaskGenInfo> cap = new ArrayList<>();
            //由于是使用map生成数，且有引用问题,导致根多选
            treeBuilder(childMeta, nodeMap, cap, k);
            res.put(k, cap);
        });
        return res;
    }


    @Override
    public List<OpsTaskGenInfo> detailForDashboard(Date nowTime, String genTime, ConditionTaskDTO condition) {
        List<OpsTaskGenInfo> resc = this.multiTaskListForDashboard(nowTime, genTime, condition);
        if (resc.size() > 500) {
            return new ArrayList<>();
        }
        // 存储多个 List<String> 的列表
        List<String> longs = new ArrayList<>();

        for (OpsTaskGenInfo info : resc) {
            if (StringUtils.hasText(info.getTaskChildIds())) {
                String[] ids = info.getTaskChildIds().split(",");
                longs.addAll(List.of(ids));
            }
        }

        //查询数据
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        queryWrapper.in(OpsTaskGenInfo::getId, longs);
        if (longs.isEmpty()) {
            return resc;
        }
        List<OpsTaskGenInfo> childMeta = baseMapper.findChildDetailByAuth(queryWrapper, condition);
        childMeta.addAll(resc);
        return childMeta;
    }


    @Override
    public List<OpsTaskGenInfo> detailStaticForDashboard(Date nowTime, String genTime, ConditionTaskDTO condition) {
        List<OpsTaskGenInfo> resc = taskParentListForDashboard(nowTime, genTime, condition);
        // 存储多个 List<String> 的列表
        List<String> longs = new ArrayList<>();
        for (OpsTaskGenInfo info : resc) {
            //如果存在子级，则添加子级id
            if (StringUtils.hasText(info.getTaskChildIds())) {
                String[] ids = info.getTaskChildIds().split(",");
                longs.addAll(List.of(ids));
            } else {
                //否则添加自身id
                longs.add(info.getId().toString());
            }
        }

        //查询数据
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        queryWrapper.in(OpsTaskGenInfo::getId, longs);
        if (longs.isEmpty()) {
            return new ArrayList<>();
        }
        List<OpsTaskGenInfo> childMeta = baseMapper.findChildDetailForStatic(queryWrapper, condition);
        childMeta.removeIf(i -> i.getTaskChildIds() != null);
        return childMeta;
    }

    public List<OpsTaskGenInfo> taskParentListForDashboard(Date nowTime, String genTime, ConditionTaskDTO condition) {
        Date nowSt = DateUtil.parse(genTime + " 00:00:00");
        Date nowEt = DateUtil.parse(genTime + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        return baseMapper.dashboardListParentId(nowTime, genTime, condition, nowSt, nowEt);
    }

    @Override
    public Map<Long, List<OpsTaskGenInfo>> findChildByLeader(Map<Long, String> p_child, ConditionTaskDTO condition) {
        List<String> longs = new ArrayList<>();
        //取所有子节点id
        p_child.forEach((k, v) -> {
            longs.addAll(List.of(v.split(",")));
        });
        //查询数据
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OpsTaskGenInfo::getId, longs);
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        queryWrapper.eq(OpsTaskGenInfo::getTaskPriority, 1);
        List<OpsTaskGenInfo> childMeta = baseMapper.findChildDetailByAuth(queryWrapper, condition);
        //取id与实体对象做映射
        Map<Long, OpsTaskGenInfo> nodeMap = new HashMap<>();
        for (OpsTaskGenInfo node : childMeta) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }
        //进行树状生成
        childMeta.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        Map<Long, List<OpsTaskGenInfo>> res = new HashMap<>();
        p_child.forEach((k, v) -> {
            List<OpsTaskGenInfo> cap = new ArrayList<>();
            //由于是使用map生成数，且有引用问题,导致根多选
            treeBuilder(childMeta, nodeMap, cap, k);
            res.put(k, cap);
        });
        return res;
    }

    @Override
    public Map<Long, List<OpsTaskGenInfo>> findChildForAudit(Map<Long, String> p_child) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTaskByTemplate(TemplateVO vo, Boolean up) {

        //先删除一次job_relation的关系数据
        deleteTemplateJobRelation(vo.getTemplate().getId());
        //更新一遍模板关联的replica的数据，包括树状关系？与个体内容？
        if (up) {
            taskTemplateService.saveOrUpdateAll(vo);
        }
        //根据类型判定是 立即生成/日常 / 特殊规则日 / 按需根据模板内的所有单个任务本身配置 来完成？
        String tempType = vo.getTemplate().getSchedulerType();
        //手动，则任务立即生成
        List<OpsTaskAttrBasicReplica> flattens = new ArrayList<>();
        flattenHandlerSelf(vo.getList(), flattens);

        if (Objects.equals(TaskConstant.MANUAL, tempType)) {
            List<OpsTaskGenInfo> saveBatch = flattens.stream().map(this::convertGenInfo).collect(Collectors.toList());
            replaceIdAndFillChildIdsAndSort(saveBatch);
            saveBatch(saveBatch);
            List<OpsTaskReminder> remindList = new ArrayList<>();
            List<OpsTaskMessageTip> messageTipList = new ArrayList<>();
            for (OpsTaskAttrBasicReplica flatten : flattens) {
                if (flatten.getRemind() != null & Boolean.TRUE.equals(flatten.getRemind())) {
                    remindList.add(new OpsTaskReminder().setTaskId(saveBatch.stream().filter(e -> e.getTaskRefId().equals(flatten.getTaskRefId())).findFirst().get().getId()));
                }
                if (StringUtils.hasText(flatten.getTipDate())) {
                    OpsTaskMessageTip opsTaskMessageTip = new OpsTaskMessageTip();
                    opsTaskMessageTip.setTipDate(flatten.getTipDate());
                    opsTaskMessageTip.setMessage(StringUtils.hasText(flatten.getTipMessage()) ? flatten.getTipMessage() : flatten.getTaskName());
                    opsTaskMessageTip.setTaskOwnerType(flatten.getTaskOwnerType());
                    opsTaskMessageTip.setTaskOwnerId(flatten.getTaskOwnerId());
                    opsTaskMessageTip.setTipType(flatten.getTipType());
                    messageTipList.add(opsTaskMessageTip);
                }
            }
            if (!remindList.isEmpty()) opsTaskReminderService.saveBatch(remindList);
            if (!messageTipList.isEmpty()) opsTaskMessageTipService.saveBatch(messageTipList);
        }
        //日常任务,工作日的
        if (Objects.equals(TaskConstant.TASK_TYPE_DAILY, tempType)) {
            List<OpsTaskJobRelation> relations = new ArrayList<>();
            for (OpsTaskAttrBasicReplica replica : flattens) {
                OpsTaskJobRelation item = new OpsTaskJobRelation();
                item.setTaskType("2");
                item.setJobId("1");
                item.setTaskId(String.valueOf(replica.getId()));
                relations.add(item);
            }
            relationService.saveBatch(relations);
        }
        //自定义的，需要选择一个触发器去执行,并且由于是模板所以关联n个单元配置对象
        //废弃
        if (Objects.equals(TaskConstant.TASK_TYPE_DYNAMIC, tempType)) {
            //只保存模板任务
           // saveSchedulerPlanByTemplate(vo.getTemplate(), vo.getTemplate().getId());
        }

    }

    private void deleteTemplateJobRelation(String id) {
        relationService.deleteDailyTaskForTemplateId(id);
    }

    @Override
    public IPage<OpsTaskGenInfo> pageCustom(IPage<OpsTaskGenInfo> page,
                                            Wrapper<OpsTaskGenInfo> wrapper,
                                            ConditionTaskDTO dto) {
        return baseMapper.pageCustom(page, wrapper, dto);
    }

    @Override
    public List<OpsTaskGenInfo> dashboardListTask(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTask(wrapper, condition);
    }

    @Override
    public List<OpsTaskGenInfo> dashboardListTaskDetail(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskDetail(wrapper, condition);
    }

    @Override
    public List<Long> dashboardListTaskCount(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskCount(wrapper, condition);
    }

    @Override
    public IPage<OpsTaskGenInfo> pageCustomAudit(IPage<OpsTaskGenInfo> pageEntity,
                                                 LambdaQueryWrapper<OpsTaskGenInfo> wrapper,
                                                 ConditionTaskDTO condition) {


        return baseMapper.pageCustomAudit(pageEntity, wrapper, condition);
    }

    @Override
    public List<OpsTaskGenInfo> listCustomAudit(LambdaQueryWrapper<OpsTaskGenInfo> wrapper,
                                                ConditionTaskDTO condition) {
        List<OpsTaskGenInfo> res = baseMapper.listCustomAudit(wrapper, condition);
        List<OpsTaskGenInfo> checkList = res.stream().filter(e -> e.getTaskCompleteStatus().equals(2) && ((e.getTaskCheckType().equals("1") && condition.getPostIds().contains(e.getTaskCheckId())) || (e.getTaskCheckType().equals("2") && e.getTaskCheckId().equals(SecureUtil.currentUserId())))).collect(Collectors.toList());
        res = res.stream().filter(e -> StrUtil.isNotBlank(e.getTaskChildIds()) && checkList.stream().anyMatch(c -> Arrays.asList(e.getTaskChildIds().split(",")).contains(c.getId().toString()))).collect(Collectors.toList());
        res.addAll(checkList);
        res = res.stream().distinct().collect(Collectors.toList());
        //排序生成树状
        res.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        //treeBuilder
        Map<Long, OpsTaskGenInfo> mapper = res.stream().collect(Collectors.toMap(OpsTaskGenInfo::getId, i -> i));
        List<Long> forOrgTree = new ArrayList<>();
        List<OpsTaskGenInfo> treeInfos = treeBuilder(res, mapper, forOrgTree);
        List<OpsTaskGenInfo> dv = res.stream().filter(i -> !forOrgTree.contains(i.getId())).collect(Collectors.toList());
        treeInfos.addAll(dv);
        return treeInfos;
    }

    private List<OpsTaskGenInfo> treeBuilder(List<OpsTaskGenInfo> res, Map<Long, OpsTaskGenInfo> cap, List<Long> forOrgTree) {
        List<OpsTaskGenInfo> fin = new ArrayList<>();
        for (OpsTaskGenInfo re : res) {
            if (re.getParentId() == 0L) {
                fin.add(re);
                forOrgTree.add(re.getId());
            } else {
                OpsTaskGenInfo p = cap.get(re.getParentId());
                if (p != null) {
                    if (p.getChildren() == null) {
                        p.setChildren(new ArrayList<>());
                    }
                    p.getChildren().add(re);
                    forOrgTree.add(re.getId());
                }
            }
        }
        return fin;
    }

    @Override
    public Map<Long, List<OpsTaskGenInfo>> findChildByAudit(Map<Long, String> p_child, ConditionTaskDTO condition) {
        List<String> longs = new ArrayList<>();
        //取所有子节点id
        p_child.forEach((k, v) -> {
            longs.addAll(List.of(v.split(",")));
        });
        //查询数据
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OpsTaskGenInfo::getId, longs);
        List<OpsTaskGenInfo> childMeta = baseMapper.findTasksForCheckRequire(queryWrapper, condition);
        //取id与实体对象做映射
        Map<Long, OpsTaskGenInfo> nodeMap = new HashMap<>();
        for (OpsTaskGenInfo node : childMeta) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }
        //进行树状生成
        childMeta.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        Map<Long, List<OpsTaskGenInfo>> res = new HashMap<>();
        p_child.forEach((k, v) -> {
            List<OpsTaskGenInfo> cap = new ArrayList<>();
            //由于是使用map生成数，且有引用问题,导致根多选
            treeBuilder(childMeta, nodeMap, cap, k);
            res.put(k, cap);
        });
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferTask(TaskTransferVO transferVO) {
        if (!opsTaskTransferConfService.delayTransfer(transferVO)) {
            return;
        }
        //哪个岗位到哪个人
        //任务追加转派备注，与修改任务转派状态
        OpsTaskGenInfo info = getById(transferVO.getTaskId());
        //其次查看转派任务是子节点还是叶子节点,如果父节点不为空0,则需要置为0 ，不然无法被转派人查询
        if (info.getParentId() != 0L && transferVO.getTransferType().equals("1")) {
            info.setParentId(0L);
        }
        List<OpsTaskGenInfo> children = new ArrayList<>();
        //子级不为空，则一样需要进行转派
        if (StringUtils.hasText(info.getTaskChildIds())) {
            List<String> ids = List.of(info.getTaskChildIds().split(","));
            LambdaQueryWrapper<OpsTaskGenInfo> query = new LambdaQueryWrapper<>();
            query.in(OpsTaskGenInfo::getId, ids);
            children = list(query);
        }
        changeInfoAttr(info, transferVO);
        if (!children.isEmpty()) {
            for (OpsTaskGenInfo child : children) {
                changeInfoAttr(child, transferVO);
            }
        }
        children.add(info);
        updateBatchById(children);
        updateParentTaskProperties(children);
    }

    /**
     * 更新父级任务属性 taskIds 将转移后的id剔除  排除完成操作影响
     * 将当前需要转派的id进行 当前树状数据查询
     * 查询的结果再进行一次去重
     * 剩下的任务对自身ids属性值进行去重后更新
     *
     * @param children 当前需要转派的任务数组
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateParentTaskProperties(List<OpsTaskGenInfo> children) {
        //查询所有转派任务的模板id
        List<String> tempId = children.stream().map(OpsTaskGenInfo::getTaskBindTemplateId).distinct().collect(Collectors.toList());
        //获取当天的任务生成时间
        String time = children.get(0).getTaskGenTime();
        //获取转派的所有任务id
        List<String> tranIds = children.stream().map(i -> i.getId() + "").collect(Collectors.toList());
        //查询当前转派的任务所在模板的所有任务集合
        List<OpsTaskGenInfo> todayInfo = list(new LambdaQueryWrapper<OpsTaskGenInfo>().in(OpsTaskGenInfo::getTaskBindTemplateId, tempId).
                eq(OpsTaskGenInfo::getTaskGenTime, time));
        //需要剔除的父任务
        List<OpsTaskGenInfo> opsTaskGenInfoList = new ArrayList<>();
        for (OpsTaskGenInfo item : todayInfo) {
            //查询是否包含此id 的父任务且不在今天转派任务内的任务
            if (item.getTaskChildIds() != null && !tranIds.contains(item.getId() + "")) {
                //剔除掉当前转派的任务id
                List<String> ids = new ArrayList<>(List.of(item.getTaskChildIds().split(",")));
                if (ids.removeAll(tranIds)) {
                    //生成新的子任务ids集合
                    item.setTaskChildIds(String.join(",", ids));
                    opsTaskGenInfoList.add(item);
                }
            }
        }
        updateBatchById(opsTaskGenInfoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchTransferTask(TaskTransferVO transferVO) {
        if (!opsTaskTransferConfService.delayTransfer(transferVO)) {
            return;
        }
        List<OpsTaskGenInfo> opsTaskGenInfoList = listByIds(transferVO.getTaskIds());
        for (OpsTaskGenInfo opsTaskGenInfoItem : opsTaskGenInfoList) {
            if (opsTaskGenInfoItem.getParentId() != 0L && opsTaskGenInfoList.stream().noneMatch(e -> e.getId().equals(opsTaskGenInfoItem.getParentId()))) {
                opsTaskGenInfoItem.setParentId(0L);
            }
            changeInfoAttr(opsTaskGenInfoItem, transferVO);
        }
        updateBatchById(opsTaskGenInfoList);
        updateParentTaskProperties(opsTaskGenInfoList);
    }

    private void changeInfoAttr(OpsTaskGenInfo info, TaskTransferVO transferVO) {
        if (transferVO.getType().equals("1")) {
            if (transferVO.getTransferType().equals("1")) {
                info.setTaskOwnerType("1");
                info.setTaskOwnerId(transferVO.getOrgId());
                info.setTaskOwnerVal(transferVO.getOrgName());
                info.setTaskTransferDesc(transferVO.getTaskDesc());
                info.setOwnerOrgId(transferVO.getOrgId());
                info.setTaskTransferStatus(1);
                info.setTaskTransferUserId(SecureUtil.currentUserId());
            } else if (transferVO.getTransferType().equals("2") && info.getTaskCheckReq() != null && info.getTaskCheckReq().equals("1")) {
                info.setTaskCheckTransferStatus(1);
                info.setTaskCheckType("1");
                info.setTaskCheckId(transferVO.getOrgId());
                info.setTaskCheckVal(transferVO.getOrgName());
                info.setTaskTransferUserId(SecureUtil.currentUserId());
            }
        }
        if (transferVO.getType().equals("2")) {
            if (transferVO.getTransferType().equals("1")) {
                info.setTaskOwnerType("2");
                info.setTaskOwnerId(transferVO.getUserId());
                info.setTaskOwnerVal(transferVO.getUserName());
                info.setTaskTransferDesc(transferVO.getTaskDesc());
                info.setTaskTransferStatus(1);
                info.setOwnerOrgId(transferVO.getOrgId());
                info.setTaskTransferUserId(SecureUtil.currentUserId());
            } else if (transferVO.getTransferType().equals("2") && info.getTaskCheckReq() != null && info.getTaskCheckReq().equals("1")) {
                info.setTaskCheckTransferStatus(1);
                info.setTaskCheckType("2");
                info.setTaskCheckId(transferVO.getUserId());
                info.setTaskCheckVal(transferVO.getUserName());
                info.setTaskTransferUserId(SecureUtil.currentUserId());
            }
        }
    }


    /**
     * 任务置完成
     *
     * @param vo 任务id
     * @return 1 附件未上传
     * 2 关联任务未完成
     * 3 正常完成
     * 4 有子节点批量完成不允许
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String taskComplete(TaskCompleteVO vo) {
        OpsTaskGenInfo info = getById(vo.getTaskId());
        info.setTaskCompleteDesc(vo.getTaskDesc());
        if (info.getContentParse() == 1 && (!vo.getTaskDesc().isBlank() || vo.getWorkAmount() != 0)) {
            List<String> desc = new ArrayList<>();
            if (vo.getTaskDesc() != null && !vo.getTaskDesc().isEmpty()) {
                desc = Arrays.stream(vo.getTaskDesc().split("[,;，]"))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .collect(Collectors.toList());
            }
            int workAmount = vo.getWorkAmount() != 0 ? vo.getWorkAmount() : 0;
            info.setTaskName(info.getTaskName() + "(*" + (desc.size() + workAmount) + ")");
        }
        //判定是否需要标记此任务不被后续自动更新
        isSignTaskCancelAutoWorkAmount(info, vo);
        info.setDelayState(isTimeDelay(info));
        info.setWorkAmount(vo.getWorkAmount());
        //如果是非叶子节点
        if (StringUtils.hasText(info.getTaskChildIds())) {
            if (StringUtils.hasText(info.getRequiredItem()) &&
                    JSONUtil.toBean(info.getRequiredItem(), RequireOptionDTO.class).getBatchCompletion() == 1) {
                return "4";
            }
            return batchTaskComplete(info, vo.getRootId(), vo.getRemind(), vo.getTipDate(), vo.getTipMessage(), vo.getTipType());
        }
        return singleTaskComplete(info, vo.getTaskId(), vo.getRootId(), false, vo.getRemind(), vo.getTipDate(), vo.getTipMessage(), vo.getTipType());
    }

    /**
     * 标记任务是否需要被后续自动更新
     * 如果任务开启了自动计数，且用户手动完成并修改了工作量，则标记为忽略自动更新
     *
     * @param info 当前任务信息
     * @param vo   用户操作参数
     */
    private void isSignTaskCancelAutoWorkAmount(OpsTaskGenInfo info, TaskCompleteVO vo) {
        if (StringUtils.hasText(info.getTaskAuditType())
                && "1".equals(info.getTaskAuditType())
                && vo.getWorkAmount() != null
                && !Objects.equals(vo.getWorkAmount(), info.getWorkAmount())) {
            opsTaskHandledRecordService.insertIgnoreBatch(List.of(info.getId().toString()), 0);
        }
    }


    @Override
    public void taskDescUpdate(TaskCompleteVO vo) {
        // 根据任务 ID 获取任务生成信息
        OpsTaskGenInfo info = getById(vo.getTaskId());
        if (info.getContentParse() == 1) {
            // 用于存储描述信息拆分后的列表
            List<String> descList = null;
            if (!vo.getTaskDesc().isBlank()) {
                // 拆分任务描述并去除前后空格
                descList = Arrays.stream(vo.getTaskDesc().split("[,;，]")).map(String::trim).collect(Collectors.toList());
            }
            int workAmount = vo.getWorkAmount();
            if (workAmount != info.getWorkAmount()) {
                opsTaskHandledRecordService.insertIgnoreBatch(List.of(info.getId().toString()), 0);
            }
            // 当 descList 不为空或者 workAmount 不为 0 时更新 taskName
            if ((descList != null && !descList.isEmpty()) || workAmount != 0) {
                int totalCount = (descList != null ? descList.size() : 0) + workAmount;
                // 更新任务名称，替换最后括号内的内容
                info.setTaskName(info.getTaskName().replaceFirst("\\(\\*[^)]*\\)$", "") + "(*" + totalCount + ")");
            }
            // 创建更新包装器
            LambdaUpdateWrapper<OpsTaskGenInfo> updateWrapper = new LambdaUpdateWrapper<>();
            // 设置要更新的任务描述
            updateWrapper.set(StringUtils.hasText(vo.getTaskDesc()), OpsTaskGenInfo::getTaskCompleteDesc, vo.getTaskDesc());

            // 设置要更新的任务名称
            if ((descList != null && !descList.isEmpty()) || workAmount != 0) {
                updateWrapper.set(OpsTaskGenInfo::getTaskName, info.getTaskName());
            }
            // 根据任务 ID 进行更新
            updateWrapper.eq(OpsTaskGenInfo::getId, info.getId());
            // 执行更新操作
            update(updateWrapper);
        } else if (vo.getWorkAmount() != 0 || vo.getTaskDesc() != null) {
            // 如果 contentParse 不为 1，更新工作总量和任务完成描述
            info.setWorkAmount(vo.getWorkAmount());
            LambdaUpdateWrapper<OpsTaskGenInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OpsTaskGenInfo::getWorkAmount, info.getWorkAmount());
            updateWrapper.set(StringUtils.hasText(vo.getTaskDesc()), OpsTaskGenInfo::getTaskCompleteDesc, vo.getTaskDesc());
            updateWrapper.eq(OpsTaskGenInfo::getId, info.getId());
            update(updateWrapper);
        }
    }

    private String isTimeDelay(OpsTaskGenInfo info) {
        if (ObjUtil.isEmpty(info.getTaskEndTime())) {
            return "0";
        }
        if (DateUtil.isSameDay(info.getTaskEndTime(), new Date())) {
            return "0";
        }
        if (DateUtil.compare(info.getTaskEndTime(), new Date()) < 0) {
            return "1";
        }
        return "0";
    }

    /**
     * 任务置为未发生状态
     *
     * @param vo 任务id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskNoExist(TaskCompleteVO vo) {
        OpsTaskGenInfo info = getById(vo.getTaskId());
        info.setTaskCompleteDesc(vo.getTaskDesc());
        //如果是非叶子节点
        if (StringUtils.hasText(info.getTaskChildIds())) {
            batchTaskSetNoExist(info);
        }
        if (vo.getRemind() != null && vo.getRemind()) {
            opsTaskReminderService.save(new OpsTaskReminder().setTaskId(info.getId()));
        }
        if (StringUtils.hasText(vo.getTipDate())) {
            OpsTaskMessageTip opsTaskMessageTip = new OpsTaskMessageTip();
            opsTaskMessageTip.setTipDate(vo.getTipDate());
            opsTaskMessageTip.setMessage(StringUtils.hasText(vo.getTipMessage()) ? vo.getTipMessage() : info.getTaskName());
            opsTaskMessageTip.setTaskOwnerType(info.getTaskOwnerType());
            opsTaskMessageTip.setTaskOwnerId(info.getTaskOwnerId());
            opsTaskMessageTip.setTipType(vo.getTipType());
            opsTaskMessageTipService.save(opsTaskMessageTip);
        }
        singleTaskSetNoExist(info, vo.getTaskId(), vo.getRootId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void multiTaskNoExist(TaskCompleteVO vo) {
        if (vo.getTaskIds() == null || vo.getTaskIds().isEmpty()) return;
        List<OpsTaskGenInfo> opsTaskGenInfoList = listByIds(vo.getTaskIds()).stream()
                .filter(e -> {
                    // 首先判断任务完成状态
                    boolean isTaskStatusValid = e.getTaskCompleteStatus() == 0 || e.getTaskCompleteStatus() == 1;
                    // 提取任务所有者类型不为空的条件
                    boolean isTaskOwnerTypeNotNull = e.getTaskOwnerType() != null;
                    // 任务所有者类型为 1 的条件
                    boolean isOwnerTypeOne = isTaskOwnerTypeNotNull && e.getTaskOwnerType().equals("1");
                    // 任务所有者类型为 2 且所有者 ID 匹配的条件
                    boolean isOwnerTypeTwoAndIdMatch = isTaskOwnerTypeNotNull && e.getTaskOwnerType().equals("2")
                            && e.getTaskOwnerId() != null && e.getTaskOwnerId().equals(SecureUtil.currentUserId());
                    // 组合所有条件
                    return isTaskStatusValid && (isOwnerTypeOne || isOwnerTypeTwoAndIdMatch);
                }).collect(Collectors.toList());
        if (opsTaskGenInfoList.isEmpty()) return;

        Set<String> referencedIds = opsTaskGenInfoList.stream()
                .filter(node -> StringUtils.hasText(node.getTaskChildIds()))
                .flatMap(node -> Arrays.stream(node.getTaskChildIds().split(",")))
                .collect(Collectors.toSet());
        opsTaskGenInfoList = opsTaskGenInfoList.stream()
                .filter(e -> !referencedIds.contains(e.getId().toString()))
                .collect(Collectors.toList());
        Set<Long> childIds = opsTaskGenInfoList.stream()
                .filter(node -> StringUtils.hasText(node.getTaskChildIds()))
                .flatMap(node -> Arrays.stream(node.getTaskChildIds().split(",")))
                .map(Long::valueOf)
                .collect(Collectors.toSet());

        childIds.addAll(opsTaskGenInfoList.stream().map(OpsTaskGenInfo::getId).collect(Collectors.toSet()));
        if (!childIds.isEmpty()) {
            update(new LambdaUpdateWrapper<OpsTaskGenInfo>()
                    .set(OpsTaskGenInfo::getTaskCompleteStatus, 5)
                    .in(OpsTaskGenInfo::getId, childIds)
                    .in(OpsTaskGenInfo::getTaskCompleteStatus, Arrays.asList(0, 1)));
        }

        OpsTaskGenInfo sample = opsTaskGenInfoList.get(0);
        if (StringUtils.hasText(sample.getTaskBindTemplateId())) {
            List<OpsTaskGenInfo> changeList = new ArrayList<>();
            List<OpsTaskGenInfo> sameTemplateTasks = list(new LambdaQueryWrapper<OpsTaskGenInfo>()
                    .eq(OpsTaskGenInfo::getTaskBindTemplateId, sample.getTaskBindTemplateId())
                    .eq(OpsTaskGenInfo::getTaskGenTime, sample.getTaskGenTime())
            );


            Map<Long, List<OpsTaskGenInfo>> parentIdTaskMap = sameTemplateTasks.stream()
                    .collect(Collectors.groupingBy(OpsTaskGenInfo::getParentId));

            List<OpsTaskGenInfo> parentCandidates = sameTemplateTasks.stream()
                    .filter(e -> StringUtils.hasText(e.getTaskChildIds()) &&
                            Arrays.stream(e.getTaskChildIds().split(","))
                                    .anyMatch(id -> vo.getTaskIds().contains(id))).sorted(Comparator.comparing(OpsTaskGenInfo::getId).reversed())
                    .collect(Collectors.toList());

            for (OpsTaskGenInfo parent : parentCandidates) {
                List<OpsTaskGenInfo> children = parentIdTaskMap.getOrDefault(parent.getId(), Collections.emptyList());

                if (children.isEmpty()) continue;

                boolean allNotOccurred = children.stream()
                        .allMatch(c -> c.getTaskCompleteStatus() == 5);

                boolean allCompletedOrNotOccurred = children.stream()
                        .allMatch(c -> c.getTaskCompleteStatus() == 3
                                || c.getTaskCompleteStatus() == 5);

                if (allNotOccurred) {
                    parent.setTaskCompleteStatus(5);
                    changeList.add(parent);
                } else if (allCompletedOrNotOccurred) {
                    parent.setTaskCompleteStatus(3);
                    changeList.add(parent);
                }
            }


            if (!changeList.isEmpty()) {
                updateBatchById(changeList);
            }
        }
    }


    private String batchTaskComplete(OpsTaskGenInfo info, String rootId, Boolean remind, String tipDate, String tipMessage, Integer tipType) {
        //先检查当前节点任务是否满足点击完成操作
        String targetStatus = singleTaskComplete(info, String.valueOf(info.getId()), rootId, true, null, tipDate, tipMessage, tipType);
        if (!targetStatus.equals("3")) {
            return targetStatus;
        }
        //先查出所有子节点，短内容
        List<String> ids = new ArrayList<>(List.of(info.getTaskChildIds().split(",")));
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getId,
                OpsTaskGenInfo::getParentId,
                OpsTaskGenInfo::getTaskName,
                OpsTaskGenInfo::getDependOnIds,
                OpsTaskGenInfo::getRequiredItem,
                OpsTaskGenInfo::getTaskCheckReq,
                OpsTaskGenInfo::getTaskAttachmentsType, OpsTaskGenInfo::getTaskOwnerType, OpsTaskGenInfo::getTaskOwnerId);
        queryWrapper.in(OpsTaskGenInfo::getId, ids);
        queryWrapper.in(OpsTaskGenInfo::getTaskCompleteStatus, 1);
        List<OpsTaskGenInfo> OpsTaskGenInfos = list(queryWrapper);
//        boolean isLeader = !opsSysOrgService.list(Wrappers.lambdaQuery(OpsSysOrg.class).eq(OpsSysOrg::getLeader, SecureUtil.currentUserId())
//                .ne(OpsSysOrg::getOrgType, "3").eq(OpsSysOrg::getDeleted, 0)).isEmpty();
        boolean isLeader=true;

        List<OpsTaskGenInfo> otherTasks = OpsTaskGenInfos.stream().filter(e -> !isLeader && !e.getTaskOwnerType().equals("1") && !(e.getTaskOwnerType().equals("2") && e.getTaskOwnerId().equals(SecureUtil.currentUserId()))).collect(Collectors.toList());
        //add fix 2025-03-25 批量完成追加属于自己能操作的任务范围丢失任务是否归属岗位类型的判定
        // 如果不是领导，则查看当前节点的负责人是否是当前用户，如果不是，则查看当前节点的审核人员是否是当前用户，如果不是，则查看当前节点的owner是否是当前用户
        List<OpsTaskGenInfo> list = OpsTaskGenInfos.stream().filter(e -> isLeader || (e.getTaskOwnerType().equals("2") && e.getTaskOwnerId().equals(SecureUtil.currentUserId())) || e.getTaskOwnerType().equals("1")).collect(Collectors.toList());
        //然后查看各自节点的依赖节点是否超出当前所有节点范围
        //如果超出，则查询超出范围的节点状态是否是已完成，是则可以进行下一步判定
        Set<String> childDepends = new HashSet<>();
        for (OpsTaskGenInfo genInfo : list) {
            if (StringUtils.hasText(genInfo.getDependOnIds())) {
                childDepends.addAll(List.of(genInfo.getDependOnIds().split(",")));
            }
        }
        if (!childDepends.isEmpty()) {
            ids.add(info.getId() + "");
            Set<String> allNow = new HashSet<>(ids);
            childDepends.removeAll(allNow);
            //如果当前批量要点及完成任务所以依赖节点超出当前范围,则查看超出范围的节点完成状态
            if (!childDepends.isEmpty()) {
                //超出状态大于1,则不满足条件
                int countDepends = baseMapper.countDependStatus(new ArrayList<>(childDepends));
                if (countDepends > 0) {
                    return "2";
                }
            }
        }
        //查看是否有必填项校验，校验不通过则也无法全部完成
        for (OpsTaskGenInfo opsTaskGenInfo : list) {
            if (!validateAttachment(opsTaskGenInfo)) {
                return "1";
            }
        }
        //两项完成则进行更新操作
        List<Long> upFinalCom = new ArrayList<>();
        List<Long> upAuditCom = new ArrayList<>();
        for (OpsTaskGenInfo genInfo : list) {
            if (genInfo.getTaskCheckReq().equals("1")) {
                upAuditCom.add(genInfo.getId());
            } else {
                upFinalCom.add(genInfo.getId());
            }
        }
        //需要复核的任务完成状态为2 待复核
        if (!upAuditCom.isEmpty())
            baseMapper.updateTaskCompleteStatusByBatch(2, upAuditCom, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), info.getDelayState());
        //不需要复核的任务完成状态为3 已完成
        if (!upFinalCom.isEmpty())
            baseMapper.updateTaskCompleteStatusByBatch(3, upFinalCom, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), info.getDelayState());
        if (otherTasks.isEmpty()) {
            singleTaskComplete(info, String.valueOf(info.getId()), rootId, false, remind, tipDate, tipMessage, tipType);
            asyncService.updateTaskOperationInfo(info, 2, SecureUtil.currentUserId());
        }

        return "3";
    }

    private void batchTaskSetNoExist(OpsTaskGenInfo info) {
        //先查出所有子节点，短内容
        List<Long> ids = new ArrayList<>(List.of(info.getTaskChildIds().split(","))).stream().map(Long::valueOf).collect(Collectors.toList());
        //不需要复核的任务完成状态为5 未发生
        if (!ids.isEmpty())
            baseMapper.updateTaskCompleteStatusByBatch(5, ids, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), info.getDelayState());
    }

    public String singleTaskComplete(OpsTaskGenInfo info, String taskId, String rootId, Boolean onlyVerify, Boolean remind, String tipDate, String tipMessage, Integer tipType) {
        if (StringUtils.hasText(info.getTaskAttachmentsType()) && !validateAttachment(info)) {
            return "1";
        }
        if (StringUtils.hasText(info.getDependOnIds()) && !validateDepends(info)) {
            return "2";
        }
        if (onlyVerify) return "3";
        int status = 3;
        if (info.getTaskCheckReq() != null && info.getTaskCheckReq().equals("1")) {
            status = 2;
        }
        //叶子节点需要查看所在分支节点除去本身外是否已全部已完成,如果是，则需要把pid节点调整也置为已完成或待复核
//         checkOtherLeafIncludeStatusAndProcess(taskId, info.getParentId(), status, info.getDelayState());
        if (StringUtils.hasText(info.getTaskChildIds())) {
            LambdaQueryWrapper<OpsTaskGenInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OpsTaskGenInfo::getId, new ArrayList<>(List.of(info.getTaskChildIds().split(","))));
            List<OpsTaskGenInfo> opsTaskGenInfoList = baseMapper.selectList(wrapper);
            if (opsTaskGenInfoList.stream().allMatch(task -> task.getTaskCompleteStatus().equals(3) || task.getTaskCompleteStatus().equals(5))) {
                status = 3;
            } else {
                status = 1;
            }
        }
        baseMapper.updateTaskCompleteStatus(status, taskId, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), info.getDelayState(), info.getWorkAmount(), info.getTaskName());
        if (remind != null && remind) {
            opsTaskReminderService.save(new OpsTaskReminder().setTaskId(Long.valueOf(taskId)));
        }
        if (StringUtils.hasText(tipDate)) {
            OpsTaskMessageTip opsTaskMessageTip = new OpsTaskMessageTip();
            opsTaskMessageTip.setTipDate(tipDate);
            opsTaskMessageTip.setMessage(StringUtils.hasText(tipMessage) ? tipMessage : info.getTaskName());
            opsTaskMessageTip.setTaskOwnerType(info.getTaskOwnerType());
            opsTaskMessageTip.setTaskOwnerId(info.getTaskOwnerId());
            opsTaskMessageTip.setTipType(tipType);
            opsTaskMessageTipService.save(opsTaskMessageTip);
        }
        correctParentStatus(rootId, status, null, null);
        asyncService.updateTaskOperationInfo(info, 1, SecureUtil.currentUserId());
        return "3";
    }


    private void correctParentStatus(String rootId, int status, List<String> operatorTaskIds, String operatorId) {
        if (StringUtils.hasText(rootId)) {
            OpsTaskGenInfo opsTaskGenInfo = getById(rootId);
            if(opsTaskGenInfo==null||opsTaskGenInfo.getTaskChildIds()==null){
                return;
            }
            LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(OpsTaskGenInfo::getId, Arrays.stream(opsTaskGenInfo.getTaskChildIds().split(",")).collect(Collectors.toList()));
            queryWrapper.orderByDesc(OpsTaskGenInfo::getId);
            List<OpsTaskGenInfo> OpsTaskGenInfoList = baseMapper.selectList(queryWrapper);
            OpsTaskGenInfoList.add(opsTaskGenInfo);
            for (OpsTaskGenInfo opsTaskGenInfoItem : OpsTaskGenInfoList) {
                if (opsTaskGenInfoItem.getTaskChildIds() != null) {
                    opsTaskGenInfoItem.setTaskChildIds(Arrays.stream(opsTaskGenInfoItem.getTaskChildIds()
                                    .split(","))
                            .filter(e -> OpsTaskGenInfoList.stream().anyMatch(task -> task.getId().equals(Long.valueOf(e)) && task.getParentId() != 0L)).collect(Collectors.joining(",")));
                }
                List<OpsTaskGenInfo> opsTaskGenInfoFilter = OpsTaskGenInfoList.stream().filter(task -> opsTaskGenInfoItem.getTaskChildIds() != null &&
                                Arrays.stream(opsTaskGenInfoItem.getTaskChildIds()
                                                .split(","))
                                        .collect(Collectors.toList())
                                        .contains(task.getId().toString()))
                        .collect(Collectors.toList());
                if (!opsTaskGenInfoFilter.isEmpty() && ((status == 5 && opsTaskGenInfoFilter.stream().allMatch(task -> task.getTaskCompleteStatus() == status || task.getTaskCompleteStatus() == 3))
                        || (status != 3 && opsTaskGenInfoFilter.stream().allMatch(task -> task.getTaskCompleteStatus() == status))
                        || (status == 3 && opsTaskGenInfoFilter.stream().allMatch(task -> task.getTaskCompleteStatus() == status || task.getTaskCompleteStatus() == 5)))) {
                    if (opsTaskGenInfoItem.getTaskCheckReq() != null && opsTaskGenInfoItem.getTaskCheckReq().equals("1") && status == 3) {
                        TaskCheckVO taskCheckVO = new TaskCheckVO();
                        if (operatorTaskIds != null && operatorId != null && opsTaskGenInfoItem.getTaskCheckType().equals("1") && Arrays.stream(opsTaskGenInfoItem.getTaskChildIds().split(",")).anyMatch(operatorTaskIds::contains)) {
                            opsTaskGenInfoItem.setOperationCheckId(operatorId);
                        }
                        changeCheckTaskInfo(opsTaskGenInfoItem, taskCheckVO, true);
                    } else {
                        if (status == 3) {
                            opsTaskGenInfoItem.setCompleteTime(new Date());
                        }
                        if (status == 5 && opsTaskGenInfoFilter.stream().anyMatch(task -> task.getTaskCompleteStatus() == 3)) {
                            opsTaskGenInfoItem.setTaskCompleteStatus(3);
                        } else if ((status == 3 && opsTaskGenInfoFilter.stream().anyMatch(e -> e.getTaskCompleteStatus().equals(3)) && opsTaskGenInfoFilter.stream().allMatch(w -> Arrays.asList(3, 5).contains(w.getTaskCompleteStatus()))) || (status == 5 && opsTaskGenInfoFilter.stream().allMatch(e -> e.getTaskCompleteStatus().equals(5)))) {
                            opsTaskGenInfoItem.setTaskCompleteStatus(status);
                        }
                    }
                    if (opsTaskGenInfoFilter.stream().anyMatch(e -> e.getTaskCompleteStatus().equals(1))) {
                        opsTaskGenInfoItem.setTaskCompleteStatus(1);
                    }

                }
            }
            updateBatchById(OpsTaskGenInfoList);
        }
    }

    private void singleTaskSetNoExist(OpsTaskGenInfo info, String taskId, String rootId) {

        baseMapper.updateTaskCompleteStatus(5, taskId, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), null, null, null);
        correctParentStatus(rootId, 5, null, null);
    }

    /**
     * 查询当前叶子任务所在的分支中，是否其他任务都已经全部已完成
     * 如果其他任务已经全部完成，且当前任务也是操作为已完成状态，则同步更新分支节点任务状态为 已完成
     * 如果当前任务状态为待复核，则不做后续执行操作
     * 如果当情人任务为已完成操，但是其他任务中有未完成或者待复核任务，则同样不做后续操作
     *
     * @param taskId     当前任务id
     * @param parentId   当前任务的父id
     * @param status     当前任务的操作状态
     * @param delayState 是否为延期完成
     */
    private void
    checkOtherLeafIncludeStatusAndProcess(String taskId, Long parentId, int status, String delayState) {
        if (parentId == 0L || status == 2) {
            //不做操作
            return;
        }
        //查询所有同级节点状态
        long count = baseMapper.taskGenInfoFindLeafAllStatus(taskId, parentId);
        if (count > 0) {
            return;
        }
        baseMapper.updateTaskCompleteStatus(status, String.valueOf(parentId), new Date(), SecureUtil.currentUserId(), "", delayState, null, null);
    }

    /**
     * 校验关联任务状态是否已完成
     *
     * @param info 任务清单
     * @return bool
     */
    private boolean validateDepends(OpsTaskGenInfo info) {
        List<String> depIds = List.of(info.getDependOnIds().split(","));
        if (depIds.isEmpty()) {
            return true;
        }
        if (depIds.contains("null")) {
            return true;
        }
        int countUnComplete = baseMapper.countDependStatus(depIds);
        return countUnComplete == 0;
    }

    /**
     * 校验附件是否上传
     *
     * @param info 当前信息
     * @return bool
     */
    private boolean validateAttachment(OpsTaskGenInfo info) {
        if (info.getTaskAttachmentsType().equals("0")) {
            return true;
        }
        long count = opsTaskGenInfoFileService.count(Wrappers.lambdaQuery(OpsTaskGenInfoFile.class).eq(OpsTaskGenInfoFile::getInfoId, info.getId()));
        return count > 0;
    }

    private void flattenHandlerSelf(List<OpsTaskAttrBasicReplica> list, List<OpsTaskAttrBasicReplica> flattens) {
        for (OpsTaskAttrBasicReplica item : list) {
            if (!item.getChild().isEmpty()) {
                flattens.add(item);
                flattenHandlerSelf(item.getChild(), flattens);
            } else {
                flattens.add(item);
            }
        }
    }

    public List<OpsTaskGenInfo> buildTree(List<OpsTaskGenInfo> nodes, Map<Long, OpsTaskGenInfo> nodeMap, Long pid) {
        List<OpsTaskGenInfo> tree = new ArrayList<>();
        for (OpsTaskGenInfo node : nodes) {
            if (node.getParentId().equals(pid)) {
                tree.add(node);
            } else {
                OpsTaskGenInfo parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }
        return tree;
    }

    public void treeBuilder(List<OpsTaskGenInfo> nodes, Map<Long, OpsTaskGenInfo> nodeMap, List<OpsTaskGenInfo> cap, Long pid) {
        for (OpsTaskGenInfo info : nodes) {
            Long parentId = info.getParentId();
            if (Objects.equals(parentId, pid)) {
                cap.add(info);
            } else {
                OpsTaskGenInfo pInfo = nodeMap.get(parentId);
                if (pInfo != null) {
                    if (pInfo.getChildren() == null) {
                        pInfo.setChildren(new ArrayList<>());
                    }
                    if (pInfo.getSetIds() == null) {
                        pInfo.setSetIds(new ArrayList<>());
                    }
                    if (!pInfo.getSetIds().contains(info.getId())) {
                        pInfo.getSetIds().add(info.getId());
                        pInfo.getChildren().add(info);
                    }
                }
            }
        }
    }

    /**
     * 任务单元生成任务列表
     *
     * @param opsTaskAttrBasic 任务单元实体类
     * @return this
     */
    @Override
    public OpsTaskGenInfo convertGenInfo(OpsTaskAttrBasic opsTaskAttrBasic) {
        OpsTaskGenInfo target = new OpsTaskGenInfo();
        BeanUtil.copyProperties(opsTaskAttrBasic, target);
        //id置空
        target.setId(null);
        //任务状态1正常
        target.setTaskProcessStatus(1);
        //任务进度1进行中
        target.setTaskCompleteStatus(1);
        //任务来源 1 任务单元
        target.setTaskRef(1);
        //任务转派 0 默认非转派任务
        target.setTaskTransferStatus(0);
        //任务默认为单节点任务
        target.setParentId(0L);
        //任务默认来源模板
        target.setTaskRefId(String.valueOf(opsTaskAttrBasic.getId()));
        if (target.getTaskAttachmentsType() == null) {
            target.setTaskAttachmentsType("0");
        }
        if (target.getTaskNameAppend() == 1 && target.getTaskAppendType() != 0) {
            target.setTaskName(appendName(target.getTaskName(), target.getTaskAppendType(), target.getDateDurType()));
        }
        //开始和截止时间,重置年月日,日常任务
        target.setTaskEndTime(hourMergeDate(target.getTaskEndTime(), target.getTaskTriggerType()));
        target.setTaskStartTime(hourMergeDate(target.getTaskStartTime(), target.getTaskTriggerType()));
        //周期任务，开始和结束时间与创建生成时间是不通的
        if (Objects.equals(opsTaskAttrBasic.getTaskType(), "period")) {
            if (opsTaskAttrBasic.getTaskStartThreshold() != 0) {
                try {
                    Date newStartTime = sysCalendarService.queryFeuDate(opsTaskAttrBasic.getTaskStartThreshold(), target.getTaskStartTime());
                    target.setTaskStartTime(newStartTime);
                } catch (RuntimeException e) {
                    log.error("周期性任务生成失败,工作日获取失败，任务id={}", target.getId(), e);
                }
            }
            if (opsTaskAttrBasic.getTaskEndThreshold() != 0) {
                try {
                    Date newEndTime = sysCalendarService.queryFeuDate(opsTaskAttrBasic.getTaskEndThreshold(), target.getTaskEndTime());
                    target.setTaskEndTime(newEndTime);
                } catch (RuntimeException e) {
                    log.error("周期性任务生成失败,工作日获取失败，任务id={}", target.getId(), e);
                }
            }
        }
        target.setTaskGenTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
        target.setCreateTime(null);
        target.setUpdateTime(null);
        return target;
    }

    private String appendName(String taskName, int taskAppendType, int af) {
        Date now = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);
        //年
        if (taskAppendType == 1) {
            taskName = ("【" + DateUtil.year(now) + "年】" + taskName);
        }
        //季度
        if (taskAppendType == 2) {
            if (af == 1) {
                cal.add(Calendar.MONTH, -3);
            }
            taskName = ("【" + DateUtil.format(cal.getTime(), "yyyy") + "年第" + DateUtil.quarter(now) + "季度】" + taskName);
        }
        //月
        if (taskAppendType == 3) {
            if (af == 1) {
                cal.add(Calendar.MONTH, -1);
            }
            taskName = ("【" + DateUtil.format(cal.getTime(), "yyyy年MM月") + "】" + taskName);
        }
        //周
        if (taskAppendType == 4) {
            if (af == 1) {
                cal.add(Calendar.WEEK_OF_MONTH, -1);
            }
            taskName = ("【" + DateUtil.format(cal.getTime(), "yyyy年MM月") + "第" + DateUtil.weekOfMonth(now) + "周】" + taskName);
        }
        //日
        if (taskAppendType == 5) {
            taskName = ("【" + DateUtil.format(now, DatePattern.CHINESE_DATE_PATTERN) + "】" + taskName);
        }
        return taskName;
    }

    /**
     * 更新重置时间
     *
     * @param date            截止时分
     * @param taskTriggerType
     * @return 新时间
     */
    private Date hourMergeDate(Date date, String taskTriggerType) {
        if (taskTriggerType != null && taskTriggerType.equals("manual")) {
            return date;
        }
        int hour = DateUtil.hour(date, true);
        int minute = DateUtil.minute(date);
        // 将 dateB 的年月日部分提取出来，秒默认为0
        Date dateBWithTime = DateUtil.beginOfDay(new Date()); // 设置为当天的 00:00:00
        dateBWithTime = DateUtil.offsetMinute(dateBWithTime, hour * 60 + minute);
        return dateBWithTime;
    }

    /**
     * 任务单元生成任务列表
     *
     * @param opsTaskAttrBasic 任务单元实体类
     * @return this
     */
    @Override
    public OpsTaskGenInfo convertGenInfoByTemp(OpsTaskAttrBasic opsTaskAttrBasic) {
        OpsTaskGenInfo target = new OpsTaskGenInfo();
        BeanUtil.copyProperties(opsTaskAttrBasic, target);
        //id置空
        target.setId(null);
        //任务状态1正常
        target.setTaskProcessStatus(1);
        //任务进度1进行中
        target.setTaskCompleteStatus(1);
        //任务来源 1 手动
        target.setTaskRef(1);
        //任务转派 0 默认非转派任务
        target.setTaskTransferStatus(0);
        //任务默认为单节点任务
        target.setParentId(0L);
        target.setTaskType("temp");
        if (target.getTaskAttachmentsType() == null) {
            target.setTaskAttachmentsType("0");
        }
        target.setTaskGenTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
        target.setCreateTime(null);
        target.setUpdateTime(null);
        return target;
    }


    /***
     * 任务清单复核通过
     * @param condition  条件内容
     * @param auditVo 复核通过内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reviewTaskProcess(ConditionTaskDTO condition, TaskCheckVO auditVo) {
        OpsTaskGenInfo pEn = getById(auditVo.getTaskId());
        List<OpsTaskGenInfo> children = StringUtils.hasText(pEn.getTaskChildIds())
                ? list(new LambdaQueryWrapper<OpsTaskGenInfo>()
                .in(OpsTaskGenInfo::getId, Arrays.asList(pEn.getTaskChildIds().split(","))))
                : new ArrayList<>();
        List<OpsTaskGenInfo> pendingReviewTasks = children.stream()
                .filter(e -> e.getTaskCompleteStatus().equals(2) && ((e.getTaskCheckType().equals("1") && condition.getPostIds().contains(e.getTaskCheckId())) || (e.getTaskCheckType().equals("2") && e.getTaskCheckId().equals(SecureUtil.currentUserId()))))
                .peek(e -> changeCheckTaskInfo(e, auditVo, false))
                .collect(Collectors.toList());
        Map<Boolean, List<Integer>> statusMap = children.stream()
                .map(OpsTaskGenInfo::getTaskCompleteStatus)
                .collect(Collectors.partitioningBy(status -> status == 3 || status == 5));

        boolean allIn3Or5 = statusMap.get(false).isEmpty();
        boolean has3 = statusMap.get(true).contains(3);
        if ((allIn3Or5 && has3) || children.isEmpty()) {
            changeCheckTaskInfo(pEn, auditVo, true);
            pendingReviewTasks.add(pEn);
        }
        if (!pendingReviewTasks.isEmpty()) {
            updateBatchById(pendingReviewTasks);
        }
        correctCheck(auditVo.getTaskId(), auditVo.getUserId());
        asyncService.updateOperationCheckInfo(pendingReviewTasks, SecureUtil.currentUserId());
    }


    private void correctCheck(String taskId, String operatorId) {
        OpsTaskGenInfo opsTaskGenInfo = getById(taskId);
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskGenInfo::getTaskBindTemplateId, opsTaskGenInfo.getTaskBindTemplateId());
        queryWrapper.eq(OpsTaskGenInfo::getTaskGenTime, opsTaskGenInfo.getTaskGenTime());
        queryWrapper.eq(OpsTaskGenInfo::getParentId, "0");
        List<OpsTaskGenInfo> OpsTaskGenInfoList = baseMapper.selectList(queryWrapper);
        Optional<OpsTaskGenInfo> opsTaskGenInfoRoot = OpsTaskGenInfoList.stream()
                .filter(task -> task.getTaskChildIds() != null).
                filter(task -> Arrays.stream(task.getTaskChildIds().split(",")).collect(Collectors.toList()).contains(taskId)).findFirst();
        opsTaskGenInfoRoot.ifPresent(info -> correctParentStatus(info.getId().toString(), 3, Collections.singletonList(taskId), operatorId));
    }

    /**
     * 任务权限过滤
     */
    @Override
    public ConditionTaskDTO taskSpecialAuthFilter(String orgId) {
        //首先判定人员属性,部门负责人,还是岗位负责人,还是部门下的多岗位的合集-负责人
        //找出所有可见权限的岗位id与人员id
        //对数据进行过滤(传入岗位切换,有值辅助过滤)
        //特殊情况，如果pid不等于0且不是null的，则是中间节点，暂时不能剔除，或导致数据残缺
        //如果岗位id不为空需要追加岗位限定过滤
        ConditionTaskDTO dto = new ConditionTaskDTO();

        if (SecureUtil.isAdmin(null)) {
            if (StringUtils.hasText(orgId)) {
                dto.setType(2);
                dto.setPostIds(List.of(orgId));
                return dto;
            }
            dto.setType(4);
            dto.setPostIds(new ArrayList<>());
            return dto;
        }
        //org为空,则需要用户自身在组织中的权限进行范围查询
        //先查是否是部门负责人,如果是，把部门下所有岗位拿出来,查询
        //如果不是，则只查询个人owner与岗位类型的可见权限
        String userId = SecureUtil.currentUserId();
        SysUser currentOrg = iSysUserService.selectUserById(Long.valueOf(userId));

        if (currentOrg.getDeptId() != null) {
            List<SysDept> sysDeptList = sysDeptMapper.selectChildrenDeptById(currentOrg.getDeptId());
            List<Long> deptList = sysDeptList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
            deptList.add(currentOrg.getDeptId());
            //如果岗位组织id为空,则按照用户权限查看
            if (!StringUtils.hasText(orgId)) {
                dto.setPostIds(deptList.stream().map(String::valueOf).collect(Collectors.toList()));
            } else {
                //否则以前端传入id为主
                dto.setPostIds(List.of(orgId));
            }
            //如果为领导,则类型调整为2
//            if (orgList.getLeader()) {
//                dto.setType(2);
//                return dto;
//            }
            //不为领导则类型调整为1
            dto.setType(2);
            //赋值用户id则
            dto.setUserId(userId);
            return dto;
        }
        return null;
    }

    /**
     * 定时任务，每日扫描允许自动延期，且任务是未完成状态的
     * 每天的下午 1点，3点，5点，10点进行扫描更改可延时的任务状态
     */
    @Override
    @Scheduled(cron = "0 0 13,15,17,22 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void timerEverDayScannerDelayTaskStatus() {
        log.info("延期任务扫描 - 开始");
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskGenInfo::getTaskGenTime, DateUtil.format(new Date(), "yyyy-MM-dd"));
        queryWrapper.eq(OpsTaskGenInfo::getTaskDeferredType, "1");
        queryWrapper.ne(OpsTaskGenInfo::getTaskCompleteStatus, "3");
        List<OpsTaskGenInfo> list = list(queryWrapper);
        Date now = new Date();
        if (list.stream().anyMatch(Objects::nonNull)) {
            List<Long> ids = new ArrayList<>();
            for (OpsTaskGenInfo info : list) {
                //如果当前任务还未到任务结束时间
                if (DateUtil.compare(info.getTaskEndTime(), now) <= 0) {
                    continue;
                }
                Date defferTime;
                try {
                    if (info.getTaskDeferredCount() == null || info.getTaskDeferredCount() == 0) {
                        log.error("任务延期更改时间操作异常,{} ,数据缺失延期阈值", info.getId());
                        continue;
                    }
                    defferTime = sysCalendarService.queryFeuDate(info.getTaskDeferredCount(), info.getTaskEndTime());
                } catch (RuntimeException e) {
                    log.error("任务延期更改时间操作异常,{}", info.getId(), e);
                    continue;
                }
                //时间更改查询时间
                ids.add(info.getId());
                LambdaUpdateWrapper<OpsTaskGenInfo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(OpsTaskGenInfo::getTaskEndTime, defferTime);
                updateWrapper.eq(OpsTaskGenInfo::getId, info.getId());
                update(updateWrapper);
            }
            log.info("延期任务扫描 - 命中 - {}", ids);
        }
        log.info("延期任务扫描 - 结束");
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboard(Date nowTime, String genTime, ConditionTaskDTO condition) {
        Date nowSt = DateUtil.parse(genTime + " 00:00:00");
        Date nowEt = DateUtil.parse(genTime + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        return baseMapper.dashboardListTaskByMultiPro(nowTime, genTime, condition, nowSt, nowEt);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeaf(Date nowTime, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeaf(nowTime, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafByLeader(Date nowTime, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeafByLeader(nowTime, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetail(Date nowTime, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeafDetail(nowTime, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailPro(Date nowTime, Date fullTimeSt, Date fullTimeEt, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeafDetailPro(nowTime, fullTimeSt, fullTimeEt, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailProForCount(Date nowTime, Date fullTimeSt, Date fullTimeEt, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeafDetailProForCount(nowTime, fullTimeSt, fullTimeEt, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailByLeader(Date nowTime, String genTime, ConditionTaskDTO condition) {
        Date nowSt = DateUtil.parse(genTime + " 00:00:00");
        Date nowEt = DateUtil.parse(genTime + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        return baseMapper.dashboardListTaskByMultiLeafDetailProByLeader(nowTime, nowSt, nowEt, genTime, condition);

    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailByLeaderForCount(Date nowTime, String genTime, ConditionTaskDTO condition) {
        Date nowSt = DateUtil.parse(genTime + " 00:00:00");
        Date nowEt = DateUtil.parse(genTime + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        return baseMapper.dashboardListTaskByMultiLeafDetailProByLeaderForCount(nowTime, nowSt, nowEt, genTime, condition);

    }


    /**
     * 更改复核通过任务状态值
     *
     * @param pEn     对象
     * @param auditVo 通过对象
     */
    private void changeCheckTaskInfo(OpsTaskGenInfo pEn, TaskCheckVO auditVo, Boolean skipValidation) {
        if (!(pEn.getTaskCompleteStatus().equals(2) || skipValidation)) return;
        //任务复核通过描述
        pEn.setTaskCheckDesc(auditVo.getTaskDesc());
        //任务复核状态置为1 通过
        pEn.setTaskCheckStatus("1");
        //任务状态置为完成
        pEn.setTaskCompleteStatus(3);
        //通过时间
        pEn.setAuditTime(new Date());
    }

    /**
     * 任务单元生成任务列表
     *
     * @param OpsTaskAttrBasicReplica 任务单元实体类副本-模板专用
     * @return this
     */
    @Override
    public OpsTaskGenInfo convertGenInfo(OpsTaskAttrBasicReplica OpsTaskAttrBasicReplica) {
        OpsTaskGenInfo target = new OpsTaskGenInfo();
        BeanUtil.copyProperties(OpsTaskAttrBasicReplica, target);
        //id置空
        //任务状态1正常
        target.setTaskProcessStatus(1);
        //任务进度1进行中
        target.setTaskCompleteStatus(1);
        //任务来源 1 任务单元
        target.setTaskRef(1);
        //任务转派 0 默认非转派任务
        target.setTaskTransferStatus(0);
        //任务配置来源
        target.setTaskRefId(OpsTaskAttrBasicReplica.getId());
        //重置任务开始和结束时间
        target.setTaskEndTime(hourMergeDate(target.getTaskEndTime(), target.getTaskTriggerType()));
        target.setTaskStartTime(hourMergeDate(target.getTaskStartTime(), target.getTaskTriggerType()));
        if (target.getTaskNameAppend() == 1 && target.getTaskAppendType() != 0) {
            target.setTaskName(appendName(target.getTaskName(), target.getTaskAppendType(), target.getDateDurType()));
        }
        //周期任务，开始和结束时间与创建生成时间是不同的
        if (Objects.equals(OpsTaskAttrBasicReplica.getTaskType(), "period")) {
            if (OpsTaskAttrBasicReplica.getTaskStartThreshold() != 0) {
                try {
                    Date newStartTime = sysCalendarService.queryFeuDate(OpsTaskAttrBasicReplica.getTaskStartThreshold(), target.getTaskStartTime());
                    target.setTaskStartTime(newStartTime);
                } catch (RuntimeException e) {
                    log.error("周期性任务生成失败,工作日获取失败，任务id={}", target.getId(), e);
                }
            }
            if (OpsTaskAttrBasicReplica.getTaskEndThreshold() != 0) {
                try {
                    Date newEndTime = sysCalendarService.queryFeuDate(OpsTaskAttrBasicReplica.getTaskEndThreshold(), target.getTaskEndTime());
                    target.setTaskEndTime(newEndTime);
                } catch (RuntimeException e) {
                    log.error("周期性任务生成失败,工作日获取失败，任务id={}", target.getId(), e);
                }
            }
        }
        target.setTaskGenTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
        target.setCreateTime(null);
        target.setUpdateTime(null);
        //任务默认为单节点任务
        return target;
    }

    @Override
    public void realDeleted(String format) {
        baseMapper.realDeleted(format);
    }

    /**
     * 批量复核动作
     *
     * @param condition 用户权限内容
     * @param checkVO   复核相关参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchReviewTaskProcess(ConditionTaskDTO condition, TaskCheckVO checkVO) {
        List<OpsTaskGenInfo> opsTaskGenInfoLists = list(
                new LambdaQueryWrapper<OpsTaskGenInfo>().in(OpsTaskGenInfo::getId, checkVO.getTaskIds())
        );
        opsTaskGenInfoLists.forEach(task -> changeCheckTaskInfo(task, checkVO, false));
        updateBatchById(opsTaskGenInfoLists);
        Set<String> childIds = opsTaskGenInfoLists.stream()
                .map(OpsTaskGenInfo::getTaskChildIds)
                .filter(Objects::nonNull)
                .flatMap(ids -> Arrays.stream(ids.split(",")))
                .collect(Collectors.toSet());
        childIds.addAll(checkVO.getTaskIds());
        if (!childIds.isEmpty()) {
            update(new LambdaUpdateWrapper<OpsTaskGenInfo>()
                    .set(OpsTaskGenInfo::getAuditTime, new Date())
                    .set(OpsTaskGenInfo::getTaskCheckStatus, "1")
                    .set(OpsTaskGenInfo::getTaskCompleteStatus, 3)
                    .set(OpsTaskGenInfo::getTaskCheckDesc, checkVO.getTaskDesc())
                    .in(OpsTaskGenInfo::getId, childIds)
            );
        }
        List<OpsTaskGenInfo> opsTaskGenInfoRootList = list(new LambdaQueryWrapper<OpsTaskGenInfo>()
                .in(OpsTaskGenInfo::getTaskBindTemplateId, opsTaskGenInfoLists.stream()
                        .map(OpsTaskGenInfo::getTaskBindTemplateId)
                        .collect(Collectors.toSet()))
                .eq(OpsTaskGenInfo::getTaskGenTime, opsTaskGenInfoLists.get(0).getTaskGenTime())
                .eq(OpsTaskGenInfo::getParentId, "0")
        );

        if (!opsTaskGenInfoRootList.isEmpty()) {
            Set<String> allTaskIds = opsTaskGenInfoRootList.stream()
                    .map(OpsTaskGenInfo::getTaskChildIds)
                    .filter(Objects::nonNull)
                    .flatMap(ids -> Arrays.stream(ids.split(",")))
                    .collect(Collectors.toSet());

            if (!allTaskIds.isEmpty()) {
                List<OpsTaskGenInfo> opsTaskGenInfoList = list(new LambdaQueryWrapper<OpsTaskGenInfo>()
                        .in(OpsTaskGenInfo::getId, allTaskIds)
                        .orderByDesc(OpsTaskGenInfo::getTaskSort)
                );

                opsTaskGenInfoList.addAll(opsTaskGenInfoRootList); // 合并根任务

                List<OpsTaskGenInfo> updateList = opsTaskGenInfoList.stream()
                        .filter(task -> {
                            if (task.getTaskChildIds() == null) return false;
                            Set<String> childIdSet = Arrays.stream(task.getTaskChildIds().split(","))
                                    .collect(Collectors.toSet());
                            List<Integer> statuses = opsTaskGenInfoList.stream()
                                    .filter(t -> childIdSet.contains(t.getId().toString()))
                                    .map(OpsTaskGenInfo::getTaskCompleteStatus)
                                    .collect(Collectors.toList());

                            return statuses.stream().allMatch(s -> s == 3 || s == 5)
                                    && statuses.contains(3);

                        })
                        .peek(task -> {
                            if (checkVO.getTaskIds() != null
                                    && checkVO.getUserId() != null
                                    && ("1").equals(task.getTaskCheckType())
                                    && Arrays.stream(task.getTaskChildIds().split(",")).anyMatch(checkVO.getTaskIds()::contains)) {
                                task.setOperationCheckId(checkVO.getUserId());
                            }
                            changeCheckTaskInfo(task, new TaskCheckVO(), true);
                        })
                        .collect(Collectors.toList());

                if (!updateList.isEmpty()) {
                    updateBatchById(updateList);
                }
            }
        }
        asyncService.updateOperationBatchCheckInfo(new ArrayList<>(childIds), SecureUtil.currentUserId());
    }


//    /**
//     * 插入定时任务待执行范围表
//     *
//     * @param info   任务单元配置信息
//     * @param taskId 任务单元id
//     */
//    private void saveSchedulerPlanByUnit(OpsTaskGenInfo info, String taskId) {
//        //日常任务只插入关联关系
//        if (TaskConstant.TASK_TYPE_DAILY.equals(info.getTaskTriggerType())) {
//            //插入定时任务表
//            jobHandler.createDailyJob(taskId);
//        }
//        //自定义任务一对一生成任务调度配置
//        if (TaskConstant.TASK_TYPE_DYNAMIC.equals(info.getTaskTriggerType())) {
//            try {
//                jobHandler.createDynamicJob(Long.valueOf(taskId), CronGeneric.generateCronExpression(Integer.parseInt(info.getTaskCronVal())), "1");
//            } catch (SchedulerException | TaskException e) {
//                throw new RuntimeException(e);
//            }
//        }
//    }

//    /**
//     * 插入定时任务待执行范围表
//     *
//     * @param info       模板配置信息
//     * @param templateId 模板id
//     */
//    private void saveSchedulerPlanByTemplate(OpsTaskTemplate info, String templateId) {
//        try {
//            jobHandler.createDynamicJob(Long.valueOf(templateId), CronGeneric.generateCronExpression(Integer.parseInt(info.getCronVal())), "2");
//        } catch (SchedulerException | TaskException e) {
//            throw new RuntimeException(e);
//        }
//    }

    /**
     * 任务重置
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void taskReset(String id) {
        OpsTaskGenInfo opsTaskGenInfo = getById(id);
        List<OpsTaskGenInfo> opsTaskGenInfoList = new ArrayList<>();
        if (StringUtils.hasText(opsTaskGenInfo.getTaskChildIds())) {
            opsTaskGenInfoList.addAll(list(new LambdaQueryWrapper<OpsTaskGenInfo>().in(OpsTaskGenInfo::getId, Arrays.stream(opsTaskGenInfo.getTaskChildIds().split(",")).collect(Collectors.toList()))));
        }
        opsTaskGenInfoList.add(opsTaskGenInfo);
        opsTaskReminderService.remove(new LambdaQueryWrapper<OpsTaskReminder>().in(OpsTaskReminder::getTaskId, opsTaskGenInfoList.stream().map(OpsTaskGenInfo::getId).collect(Collectors.toList())));

        LambdaQueryWrapper<OpsTaskGenInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(OpsTaskGenInfo::getTaskChildIds, id);
        lambdaQueryWrapper.eq(OpsTaskGenInfo::getTaskGenTime, opsTaskGenInfo.getTaskGenTime());
        lambdaQueryWrapper.eq(OpsTaskGenInfo::getTaskCompleteStatus, opsTaskGenInfo.getTaskCompleteStatus());
        opsTaskGenInfoList.addAll(list(lambdaQueryWrapper));
        opsTaskHandledRecordService.removeByIds(opsTaskGenInfoList.stream().map(OpsTaskGenInfo::getId).collect(Collectors.toList()));

        for (OpsTaskGenInfo opsTaskGenInfoItem : opsTaskGenInfoList) {
            opsTaskGenInfoItem.setTaskCompleteDesc("");
            opsTaskGenInfoItem.setTaskCheckDesc("");
            opsTaskGenInfoItem.setWorkAmount(0);
            if (opsTaskGenInfoItem.getContentParse() == 1) {
                opsTaskGenInfoItem.setTaskName(opsTaskGenInfo.getTaskName().replaceFirst("\\(\\*[^)]*\\)$", ""));
            }
            opsTaskGenInfoItem.setTaskCompleteStatus(1);
        }
        updateBatchById(opsTaskGenInfoList);
        asyncService.updateOperationSetNull(opsTaskGenInfo, SecureUtil.currentUserId());
    }


    @Override
    public List<OpsTaskGenInfo> allTaskForNowDay(String date) {
        return baseMapper.allTaskForNowDay(date);
    }

    @Override
    public List<OpsTaskGenInfo> allTaskForNowDayV1(String date) {
        return baseMapper.allTaskForNowDayV1(date);
    }

    @Override
    public List<OpsTaskGenInfo> checkTaskForUser(String date, String userId) {
        return baseMapper.checkTaskForUser(date, userId);
    }

    /**
     * 统计指标详情列表相关
     *
     * @param date
     * @param userId
     * @return
     */
    @Override
    public List<OpsTaskGenInfo> checkTaskForUserDetail(String date, String userId) {
        return baseMapper.checkTaskForUserDetail(date, userId);
    }

    @Override
    public List<OpsTaskGenInfo> checkTaskListForLeaderOrUser(String genTime, ConditionTaskDTO condition) {
        //如果类型等于2 那么查询所有复核岗位id等于范围内的且复核完成的
        //如果不等于2 ，则认为最高权限查询所有
        //时间规则依然是，日常的只查当天的，周期的按照当天等于截止日期

        //普通员工
        if (condition.getType() != null && condition.getType() == 1) {
            //复核条目目前需要追加完成
            List<OpsTaskGenInfo> checkOpsTaskGenInfoList = checkTaskForUser(genTime, condition.getUserId());
            //过滤复核任务 - 日常任务则必须是今天的，  周期任务则结束时间是今天的才计入 完成和总数
            return checkOpsTaskGenInfoList.stream().filter(i -> i.getTaskCheckReq().equals("1") &&
                    (i.getTaskType().equals("daily") && i.getTaskGenTime().equals(genTime)) ||
                    ((i.getTaskType().equals("period") || i.getTaskType().equals("temp")) && DateUtil.format(i.getCompleteTime(), "yyyy-MM-dd").equals(genTime))).collect(Collectors.toList());
        }
        //领导直接查范围
        return baseMapper.checkTaskForLeader(genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> checkTaskListForLeader(String genTime, ConditionTaskDTO condition) {
        //普通员工
        //领导直接查范围
        return baseMapper.checkTaskForLeaderByLevelHi(genTime, condition);
    }

    /**
     * 统计指标详情列表相关
     *
     * @param genTime
     * @param condition
     * @return
     */
    @Override
    public List<OpsTaskGenInfo> checkTaskListForLeaderOrUserDetail(String genTime, ConditionTaskDTO condition) {
        //如果类型等于2 那么查询所有复核岗位id等于范围内的且复核完成的
        //如果不等于2 ，则认为最高权限查询所有
        //时间规则依然是，日常的只查当天的，周期的按照当天等于截止日期

        //普通员工
        if (condition.getType() != null && condition.getType() == 1) {
            //复核条目目前需要追加完成和未完成
            List<OpsTaskGenInfo> checkOpsTaskGenInfoList = checkTaskForUserDetail(genTime, condition.getUserId());
            //过滤复核任务 - 日常任务则必须是今天的，  周期任务则结束时间是今天的才计入 完成和总数
            return checkOpsTaskGenInfoList.stream().filter(i -> i.getTaskCheckReq().equals("1") &&
                    (i.getTaskType().equals("daily") && i.getTaskGenTime().equals(genTime)) ||
                    ((i.getTaskType().equals("period") || i.getTaskType().equals("temp")) && DateUtil.format(i.getTaskEndTime(), "yyyy-MM-dd").equals(genTime))).collect(Collectors.toList());
        }
        //领导直接查范围
        return baseMapper.checkTaskForLeaderDetail(genTime, condition);
    }

    /**
     * 通过子节点id获取父节点数据
     *
     * @param ids
     * @return
     */
    @Override
    public List<OpsTaskGenInfo> getParentListByChildIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return baseMapper.getParentListByChildIds(ids);
    }

    @Override
    public List<OpsTaskGenInfo> dashboardTempListTaskLeaf(Wrapper<OpsTaskGenInfo> opsTaskGenInfoWrapper, ConditionTaskDTO condition) {
        return baseMapper.dashboardTempListTaskLeaf(opsTaskGenInfoWrapper, condition);
    }

    @Override
    public List<OpsTaskGenInfo> listByTemplateIdAndNowDate(String templateId, String nowDate) {
        return baseMapper.listByTemplateIdAndNowDate(templateId, nowDate);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardByLeader(Date now, String genTime, ConditionTaskDTO condition) {
        // return baseMapper.dashboardListTaskByMultiByLeader(now, genTime, condition);
        Date nowSt = DateUtil.parse(genTime + " 00:00:00");
        Date nowEt = DateUtil.parse(genTime + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        return baseMapper.dashboardListTaskByMultiByLeaderPro(now, genTime, condition, nowSt, nowEt);
    }

    @Override
    public void todoListExport(List<OpsTaskGenInfo> list, String startDate, String endDate, HttpServletResponse httpServletResponse, String orgName) {

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"编号", "内容", "开始时间", "结束时间", "备注", "经办", "复核"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }
        list.sort(Comparator.comparing(OpsTaskGenInfo::getId));

        Map<Long, OpsTaskGenInfo> nodeMap = new HashMap<>();
        List<OpsTaskGenInfo> roots = new ArrayList<>();

        for (OpsTaskGenInfo node : list) {
            if (node.getChildren() == null) node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }

        for (OpsTaskGenInfo node : list) {
            if (node.getParentId() == 0 || list.stream().noneMatch(l -> l.getId().equals(node.getParentId()))) {
                roots.add(node);
            } else {
                nodeMap.get(node.getParentId()).getChildren().add(node);
            }
        }

        traverse(roots, "");

        // 填充数据
        for (int i = 0; i < list.size(); i++) {
            OpsTaskGenInfo opsTaskGenInfo = list.get(i);
            Row row = sheet.createRow(i + 1);

            row.createCell(0).setCellValue(opsTaskGenInfo.getTaskNo());
            row.createCell(1).setCellValue(opsTaskGenInfo.getTaskName());
            row.createCell(2).setCellValue(formatDateToString(opsTaskGenInfo.getTaskStartTime()));
            row.createCell(3).setCellValue(formatDateToString(opsTaskGenInfo.getTaskEndTime()));
            row.createCell(4).setCellValue(opsTaskGenInfo.getTaskCompleteDesc());
            row.createCell(5).setCellValue(opsTaskGenInfo.getTaskOwnerVal());
            row.createCell(6).setCellValue(opsTaskGenInfo.getTaskCheckVal());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        try {
            httpServletResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpServletResponse.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(new String((orgName + startDate + "至" + endDate).getBytes(), "UTF-8"), "UTF-8") + ".xlsx");
            ServletOutputStream outputStream = httpServletResponse.getOutputStream();
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private static void traverse(List<OpsTaskGenInfo> list, String taskNo) {
        for (int i = 0; i < list.size(); i++) {
            OpsTaskGenInfo opsTaskGenInfo = list.get(i);
            opsTaskGenInfo.setTaskNo(taskNo.isEmpty() ? String.valueOf(i + 1) : taskNo + "-" + (i + 1));
            traverse(opsTaskGenInfo.getChildren() != null ? opsTaskGenInfo.getChildren() : new ArrayList<>(), opsTaskGenInfo.getTaskNo());
        }
    }

    private static String formatDateToString(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    @Override
    public List<WorkAmountByOrgVO> getWorkAmountByYearAndMonth(String year, String month) {
//        int yearInt = Integer.parseInt(year);
//        LocalDateTime startTime = LocalDateTime.of(yearInt, 1, 1, 0, 0);
//        LocalDateTime endTime = startTime.plusYears(1);
//
//
//        List<WorkAmountByUserVO> userWorkList = baseMapper.getWorkAmountByUser(startTime, endTime);
//        List<OpsSysOrg> orgList = opsSysOrgService.list();
//        List<OpsTaskGenInfo> taskList = baseMapper.getTaskCompleteDesc(startTime, endTime);
//
//
//        List<String> enableOrg = orgList.stream().filter(e -> e.getStSign() == 1).map(OpsSysOrg::getId).map(String::valueOf).collect(Collectors.toList());
//
//        List<SystemUser> systemUserList = systemUserService.list(
//                new LambdaQueryWrapper<SystemUser>()
//                        .isNotNull(SystemUser::getOwnerDept)
//                        .select(SystemUser::getId, SystemUser::getName, SystemUser::getOwnerDept)
//        );
//
//
//        Map<String, String> userNameMap = systemUserList.stream()
//                .collect(Collectors.toMap(SystemUser::getId, SystemUser::getName, (v1, v2) -> v1));
//        Map<String, List<String>> orgUserMap = systemUserList.stream()
//                .collect(Collectors.groupingBy(
//                        SystemUser::getOwnerDept,
//                        Collectors.mapping(SystemUser::getId, Collectors.toList())
//                ));
//
//        Map<String, String> orgParentMap = buildOrgParentMap(orgList);
//        Map<String, String> orgNameMap = orgList.stream()
//                .collect(Collectors.toMap(org -> org.getId().toString(), OpsSysOrg::getOrgName));
//
//        List<String> ymList = IntStream.rangeClosed(1, 12)
//                .mapToObj(m -> YearMonth.of(yearInt, m).toString())
//                .collect(Collectors.toList());
//
//        Set<String> synchronizedKeys = new HashSet<>();
//
//        for (WorkAmountByUserVO userWorkItem : userWorkList) {
//            String parentOrgId = orgParentMap.get(userWorkItem.getOrgId());
//            String syncKey = userWorkItem.getUserId() + "_" + "_" + parentOrgId;
//
//            if (!synchronizedKeys.add(syncKey)) {
//                continue;
//            }
//
//            List<WorkAmountByUserVO> sameUserSameParentOrgList = userWorkList.stream()
//                    .filter(item -> item.getUserId().equals(userWorkItem.getUserId())
//                            && parentOrgId.equals(orgParentMap.get(item.getOrgId())))
//                    .collect(Collectors.toList());
//
//            if (sameUserSameParentOrgList.size() == 1) {
//                continue;
//            }
//            // 这里取的是所有月的，但是相加的时候只取每个不同orgid中的yearnum相加
//
//            BigDecimal totalYearNum = sameUserSameParentOrgList.stream()
//                    .collect(Collectors.collectingAndThen(
//                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WorkAmountByUserVO::getOrgId))),
//                            ArrayList::new
//                    )).stream()
//                    .map(WorkAmountByUserVO::getYearNum)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            sameUserSameParentOrgList.forEach(item -> item.setYearNum(totalYearNum));
//            // 同一个人、同一个月、同一个大部门但不同岗位的年数据同步
//        }
//
//
//        processTaskData(taskList, userWorkList);
//
//        List<WorkAmountByOrgVO> workAmountByOrgVOList = buildOrgWorkAmountList(
//                orgUserMap, orgNameMap, orgParentMap, userNameMap, ymList, userWorkList);
//
//        processRemainingWorkAmount(userWorkList, workAmountByOrgVOList, orgParentMap, userNameMap, ymList);
//
//        return workAmountByOrgVOList.stream().filter(e -> enableOrg.contains(e.getOrgId())).collect(Collectors.toList());
        return new ArrayList<>();
    }

//    private Map<String, String> buildOrgParentMap(List<OpsSysOrg> orgList) {
//        Set<Long> rootOrgIds = orgList.stream()
//                .filter(o -> "2".equals(o.getOrgType()))
//                .map(OpsSysOrg::getId)
//                .collect(Collectors.toSet());
//
//        Map<String, String> orgParentMap = new HashMap<>(orgList.size());
//        orgList.stream()
//                .filter(org -> !"2".equals(org.getOrgType()) && StringUtils.hasText(org.getAncestors()))
//                .forEach(org -> {
//                    String[] ancestors = org.getAncestors().split(",");
//                    for (String ancestorId : ancestors) {
//                        if (rootOrgIds.contains(Long.valueOf(ancestorId))) {
//                            orgParentMap.put(org.getId().toString(), ancestorId);
//                            break;
//                        }
//                    }
//                });
//        return orgParentMap;
//    }

    private List<WorkAmountByOrgVO> buildOrgWorkAmountList(
            Map<String, List<String>> orgUserMap,
            Map<String, String> orgNameMap,
            Map<String, String> orgParentMap,
            Map<String, String> userNameMap,
            List<String> ymList,
            List<WorkAmountByUserVO> userWorkList) {

        List<WorkAmountByOrgVO> workAmountByOrgVOList = new ArrayList<>();

        for (Map.Entry<String, List<String>> orgUser : orgUserMap.entrySet()) {
            String orgId = orgUser.getKey();
            List<String> orgUserIds = orgUser.getValue();
            List<WorkAmountByUserVO> children = new ArrayList<>();

            for (String orgUserId : orgUserIds) {
                BigDecimal yearNum = userWorkList.stream()
                        .filter(e -> e.getUserId() != null && e.getUserId().equals(orgUserId) &&
                                orgParentMap.get(e.getOrgId()).equals(orgId))
                        .findFirst()
                        .map(WorkAmountByUserVO::getYearNum)
                        .orElse(BigDecimal.ZERO);

                for (String ym : ymList) {
                    List<WorkAmountByUserVO> workAmountByUserVOFilter = userWorkList.stream()
                            .filter(e -> e.getUserId() != null && e.getUserId().equals(orgUserId) &&
                                    orgParentMap.get(e.getOrgId()).equals(orgId) && e.getMonth().equals(ym))
                            .collect(Collectors.toList());

                    if (!workAmountByUserVOFilter.isEmpty()) {
                        WorkAmountByUserVO current = workAmountByUserVOFilter.get(0);
                        current.setMonthNum(workAmountByUserVOFilter.stream()
                                .map(WorkAmountByUserVO::getMonthNum)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
                        current.setUserName(userNameMap.get(current.getUserId()));
                        children.add(current);
                        userWorkList.removeAll(workAmountByUserVOFilter);
                    } else {
                        WorkAmountByUserVO workAmountByUserVO = new WorkAmountByUserVO();
                        workAmountByUserVO.setMonth(ym);
                        workAmountByUserVO.setYearNum(yearNum);
                        workAmountByUserVO.setUserId(orgUserId);
                        workAmountByUserVO.setMonthNum(BigDecimal.ZERO);
                        workAmountByUserVO.setUserName(userNameMap.get(orgUserId));
                        children.add(workAmountByUserVO);
                    }
                }
            }

            WorkAmountByOrgVO workAmountByOrgVO = new WorkAmountByOrgVO();
            workAmountByOrgVO.setOrgId(orgId);
            workAmountByOrgVO.setOrgName(orgNameMap.get(orgId));
            workAmountByOrgVO.setChildren(children);
            workAmountByOrgVOList.add(workAmountByOrgVO);
        }

        return workAmountByOrgVOList;
    }

    private void processRemainingWorkAmount(
            List<WorkAmountByUserVO> userWorkList,
            List<WorkAmountByOrgVO> workAmountByOrgVOList,
            Map<String, String> orgParentMap,
            Map<String, String> userNameMap,
            List<String> ymList) {

        List<WorkAmountByUserVO> allChildren = workAmountByOrgVOList.stream()
                .filter(parent -> parent.getChildren() != null) // 过滤掉 null
                .flatMap(parent -> parent.getChildren().stream())
                .collect(Collectors.toList());


        // 如果其他已经分配的人员在另一部门，那么我应该把我的yearNum分给他以保证工作量的统一
        for (WorkAmountByUserVO eitherOrgWork : userWorkList) {
            Optional<WorkAmountByOrgVO> workAmountByOrgVO = workAmountByOrgVOList.stream()
                    .filter(e -> e.getOrgId().equals(orgParentMap.get(eitherOrgWork.getOrgId())))
                    .findFirst();
            if (workAmountByOrgVO.isEmpty()) continue;

            List<WorkAmountByUserVO> workAmountByUserVOList = new ArrayList<>();

            List<WorkAmountByUserVO> sameUserList = allChildren.stream().filter(e -> e.getUserId().equals(eitherOrgWork.getUserId())).collect(Collectors.toList());
            if (!sameUserList.isEmpty()) {
                BigDecimal sYearNum = sameUserList.get(0).getYearNum().add(eitherOrgWork.getYearNum());
                sameUserList.forEach(e -> e.setYearNum(sYearNum));
                eitherOrgWork.setYearNum(sYearNum);
            }
            for (String ym : ymList) {
                if (eitherOrgWork.getMonth().equals(ym)) {
                    eitherOrgWork.setUserName(userNameMap.get(eitherOrgWork.getUserId()));
                    workAmountByUserVOList.add(eitherOrgWork);
                } else {
                    WorkAmountByUserVO workAmountByUserVO = SerializationUtils.clone(eitherOrgWork);
                    workAmountByUserVO.setMonthNum(BigDecimal.ZERO);
                    workAmountByUserVO.setMonth(ym);
                    workAmountByUserVO.setUserName(userNameMap.get(workAmountByUserVO.getUserId()));
                    workAmountByUserVOList.add(workAmountByUserVO);
                }
            }
            workAmountByUserVOList.addAll(workAmountByOrgVO.get().getChildren());
            workAmountByOrgVO.get().setChildren(workAmountByUserVOList);
        }
    }

    private void processTaskData(List<OpsTaskGenInfo> taskList, List<WorkAmountByUserVO> userWorkList) {
        for (OpsTaskGenInfo task : taskList) {
            if (!StringUtils.hasText(task.getTaskCompleteDesc())) continue;

            String orgId = "1".equals(task.getTaskCheckReq()) ? task.getCheckOrgId() : task.getOwnerOrgId();
            String userId = "1".equals(task.getTaskCheckReq()) ?
                    ("1".equals(task.getTaskCheckType()) ? task.getOperationCheckId() : task.getTaskCheckId())
                    : ("1".equals(task.getTaskOwnerType()) ? task.getOperationCompleteId() : task.getTaskOwnerId());
            if (userId == null) continue;
            // 这里要加上一个 如果getTaskCheckReq为1  那么我要为这条任务的经办再算一次
            compileUserWorkList(task, userWorkList, orgId, userId);
            if ("1".equals(task.getTaskCheckReq())) {
                compileUserWorkList(task, userWorkList, orgId, "1".equals(task.getTaskOwnerType()) ? task.getOperationCompleteId() : task.getTaskOwnerId());
            }
        }
    }

    private void compileUserWorkList(OpsTaskGenInfo task, List<WorkAmountByUserVO> userWorkList, String orgId, String userId) {
        BigDecimal num = BigDecimal.valueOf(task.getTaskCompleteDesc().split("[,;，]").length);
        String month = DateUtil.format(task.getCompleteTime(), "YYYY-MM");

        List<WorkAmountByUserVO> sameUserAmount = userWorkList.stream()
                .filter(e -> e.getUserId() != null && e.getUserId().equals(userId) &&
                        e.getOrgId() != null && e.getOrgId().equals(orgId))
                .collect(Collectors.toList());

        if (!sameUserAmount.isEmpty()) {
            BigDecimal newYearNum = sameUserAmount.get(0).getYearNum().add(num);
            sameUserAmount.forEach(e -> e.setYearNum(newYearNum));

            Optional<WorkAmountByUserVO> filter = sameUserAmount.stream()
                    .filter(e -> e.getMonth().equals(month))
                    .findFirst();

            if (filter.isPresent()) {
                WorkAmountByUserVO filterValue = filter.get();
                filterValue.setMonthNum(filterValue.getMonthNum().add(num));
            } else {
                addNewWorkAmount(userWorkList, userId, orgId, month, num, newYearNum);
            }
        } else {
            addNewWorkAmount(userWorkList, userId, orgId, month, num, num);
        }
    }

    private void addNewWorkAmount(List<WorkAmountByUserVO> userWorkList, String userId, String orgId,
                                  String month, BigDecimal monthNum, BigDecimal yearNum) {
        WorkAmountByUserVO workAmountByUserVO = new WorkAmountByUserVO();
        workAmountByUserVO.setMonthNum(monthNum);
        workAmountByUserVO.setYearNum(yearNum);
        workAmountByUserVO.setUserId(userId);
        workAmountByUserVO.setOrgId(orgId);
        workAmountByUserVO.setMonth(month);
        userWorkList.add(workAmountByUserVO);
    }

    @Override
    public List<OpsTaskGenInfo> getAmountTaskByUser(String year, String month, String userId, String orgId) {
        int yearInt = Integer.parseInt(year);
        int monthInt = Integer.parseInt(month);
        LocalDateTime startTime = LocalDateTime.of(yearInt, monthInt, 1, 0, 0);
        LocalDateTime endTime = startTime.plusMonths(1);
       // List<String> orgList = opsSysOrgService.getChildOrgByRoot(orgId).stream().map(OpsSysOrg::getId).map(String::valueOf).collect(Collectors.toList());
        return baseMapper.getAmountTaskByUser(startTime, endTime, userId, new ArrayList<>());
    }

    @Override
    public List<OpsTaskGenInfo> getYearAmountTaskByUser(String year, String userId) {
        int yearInt = Integer.parseInt(year);
        LocalDateTime startTime = LocalDateTime.of(yearInt, 1, 1, 0, 0);
        LocalDateTime endTime = startTime.plusYears(1);
        return baseMapper.getAmountTaskByUser(startTime, endTime, userId, null);
    }

    @Override
    public void removeInvalidTasks() {
        remove(new LambdaQueryWrapper<OpsTaskGenInfo>().eq(OpsTaskGenInfo::getDeleted, CommonConstant.DEL_FLAG_1).lt(OpsTaskGenInfo::getCreateTime, DateUtil.beginOfDay(new Date())));
    }

}




