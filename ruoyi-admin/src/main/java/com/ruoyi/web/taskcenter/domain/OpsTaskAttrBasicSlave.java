package com.ruoyi.web.taskcenter.domain;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName OPS_TASK_ATTR_BASIC_SLAVE
 */
@TableName(value = "OPS_TASK_ATTR_BASIC_SLAVE")
@Data
@EqualsAndHashCode
@ToString
public class OpsTaskAttrBasicSlave implements Serializable {
    /**
     *
     */
    @TableId
    private String id;

    /**
     * 父id
     */
    private String parentId;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务单元状态 0 未上线 1 上线
     */
    private Integer taskStatus;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务触发类型 （manual 手动 auto 自动)
     */
    private String taskTriggerType;

    /**
     * 任务触发器引用单元id
     */
    private String taskTriggerId;

    /**
     * 任务配置定时任务表达式
     */
    private String taskCronVal;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 任务完成方式（manual 手动 auto 自动)
     */
    private String taskCompleteType;

    /**
     * 任务如果为自动完成，则需要配置脚本来源，引用单元id
     */
    private String taskCompleteUnitId;

    /**
     * 任务是否需要自动稽核( 0 否  1是)
     */
    private String taskAuditType;

    /**
     * 如果需要自动稽核，引用脚本来源id
     */
    private String taskAuditUnitId;

    /**
     * 告警通知方式 1 站内工作台 2 邮件
     */
    private String taskWarnNotice;

    /**
     * 任务优先级 （1 高 2 中 3低）
     */
    private String taskPriority;

    /**
     * 任务紧急程度(1紧急 2 一般 3 普通)
     */
    private String taskLevel;

    /**
     * 任务是否需要附件（0否1 是）
     */
    private String taskAttachmentsType;

    /**
     * 任务归属是谁 1 岗位 2 具体人员
     */
    private String taskOwnerType;

    /**
     * 任务归属id ，岗位id或者人员id
     */
    private String taskOwnerId;

    /**
     * 任务归属真实值，冗余字段
     */
    private String taskOwnerVal;

    /**
     * 任务是否需要复核 （0否1 是）
     */
    private String taskCheckReq;

    /**
     * 复核权限对象类型(1岗位2人员)
     */
    private String taskCheckType;

    /**
     * 复核权限对象id
     */
    private String taskCheckId;

    /**
     * 复核权限对象真实值，冗余字段
     */
    private String taskCheckVal;

    /**
     *
     */
    private Date taskStartTime;

    /**
     *
     */
    private Date taskEndTime;

    /**
     * 任务属性标签
     */
    private String taskTags;

    /**
     * 任务权限类型(1到机构 2 部门 3 岗位 4 具体人员)
     */
    private String taskAuthType;

    /**
     * 任务归属id
     */
    private String taskAuthId;

    /**
     * 任务归属真实值，冗余字段
     */
    private String taskAuthVal;

    /**
     * 任务范围(
     * QUERY 查询权限,
     * DOWN 下载权限,
     * EDIT 编辑权限,
     * TRANSFER,转派
     * INVALID,作废
     * AUDIT,复核
     * ,ALL所有)
     */
    private String taskAuthScope;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private String updateBy;

    /**
     * 删除标记
     */
    private String deleted;

    /**
     * 任务是否可以顺延 0 否 1是
     */
    private String taskDeferredType;

    /**
     * 任务顺延几天，当前几天是默认工作日
     */
    private Integer taskDeferredCount;

    /**
     * 任务完成需要依赖节点已经完成
     */
    private String dependOnIds;

    /**
     * 必填项校验，点击完成前校验任务自身那些属性是不能为空
     */
    private String requiredItem;

    /**
     * 任务生成来源-任务单元id
     */
    private String taskRefId;

    /**
     * 冗余岗位id方便查询
     */
    private String ownerOrgId;

    /**
     * 冗余复核岗位id
     */
    private String checkOrgId;

    /**
     * 真实允许开始时间阈值
     */
    private Integer taskStartThreshold;

    /**
     * 真实允许结束时间阈值
     */
    private Integer taskEndThreshold;

    /**
     * 任务绑定模板id
     */
    private Long taskBindTemplateId;

    /**
     *
     */
    private Integer taskSort;

    /**
     * 数据最低权限下可见性0 否 1 是
     */
    private Integer accessLevel;

    /**
     *
     */
    private Integer workAmount;

    /**
     *
     */
    private String workAmountFlag;

    /**
     *
     */
    private Integer importStatus;

    /**
     * 任务创建追加日期标记
     */
    private Integer taskNameAppend;

    /**
     * 任务创建追加日期标记类型 1 年 2 季度 3 月 4 周
     */
    private Integer taskAppendType;

    /**
     * 任务创建类型 0 update 1 insert
     */
    private Integer taskCreateType;

    /**
     * 副本转派id
     */
    private Long transferId;

    /**
     * 内容解析 默认0 0 不存在 1 存在
     */
    private int contentParse;


    /**
     * 标题配置-日期 季 月 周 的情况下业务特需 -1个周
     */
    private int dateDurType;


    public OpsTaskAttrBasicSlave convert(OpsTaskAttrBasicReplica replica, Long trId) {
        OpsTaskAttrBasicSlave s = new OpsTaskAttrBasicSlave();
        BeanUtil.copyProperties(replica, s);
        s.setTransferId(trId);
        return s;
    }

    public OpsTaskAttrBasicReplica convert(OpsTaskAttrBasicSlave replica) {
        OpsTaskAttrBasicReplica s = new OpsTaskAttrBasicReplica();
        BeanUtil.copyProperties(replica, s);
        return s;
    }

    /**
     * 是否是特殊统计任务 默认0 0 不存在 1 存在
     */
    private int taskReportFlag;

    /**
     * 工作量计数
     */
    private int taskReportCount;
}