package com.ruoyi.web.taskcenter.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.web.taskcenter.domain.ConditionTaskDTO;
import com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo;
import com.ruoyi.web.taskcenter.domain.WorkAmountByUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_gen_info】的数据库操作Mapper
 * @createDate 2024-07-04 10:28:39
 * @Entity OpsTaskGenInfo
 */
@Mapper
public interface OpsTaskGenInfoMapper extends BaseMapper<OpsTaskGenInfo> {


    IPage<OpsTaskGenInfo> pageCustom(IPage<OpsTaskGenInfo> page,
                                     @Param("ew") Wrapper<OpsTaskGenInfo> wrapper,
                                     @Param("dc") ConditionTaskDTO dto);

    IPage<OpsTaskGenInfo> pageCustomAudit(IPage<OpsTaskGenInfo> pageEntity,
                                          @Param("ew") LambdaQueryWrapper<OpsTaskGenInfo> wrapper,
                                          @Param("dc") ConditionTaskDTO condition);

    void updateTaskCompleteStatus(@Param("st") Integer status, @Param("id") String taskId,
                                  @Param("date") Date date, @Param("userId") String userId,
                                  @Param("desVal") String taskCompleteDesc, @Param("state") String state,
                                  @Param("count") Integer count,
                                  @Param("taskName") String taskName);

    int countDependStatus(@Param("dep") List<String> depIds);

    void updateTaskCompleteStatusByBatch(@Param("st") Integer status, @Param("ids") List<Long> ids, @Param("date") Date date,
                                         @Param("userId") String userId, @Param("desVal") String taskCompleteDesc, @Param("state") String state);

    List<OpsTaskGenInfo> findTasksForCheckRequire(@Param("ew") LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> dashboardListTask(@Param("ew") Wrapper<OpsTaskGenInfo> wrapper, @Param("dc") ConditionTaskDTO condition);

    /**
     * 统计指标详情列表相关
     *
     * @param wrapper
     * @param condition
     * @return
     */
    List<OpsTaskGenInfo> dashboardListTaskDetail(@Param("ew") Wrapper<OpsTaskGenInfo> wrapper, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> dashboardListTaskByMulti(@Param("nowTime") Date time, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> dashboardListTaskByMultiLeaf(@Param("nowTime") Date time, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> dashboardListTaskByMultiLeafByLeader(@Param("nowTime") Date time, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);


    /**
     * 统计指标详情列表相关
     *
     * @param time
     * @param genTime
     * @param condition
     * @return
     */
    List<OpsTaskGenInfo> dashboardListTaskByMultiLeafDetail(@Param("nowTime") Date time, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> dashboardListTaskByMultiLeafDetailPro(@Param("nowTime") Date time,@Param("fullTimeSt") Date fullTimeSt,@Param("fullTimeEt") Date fullTimeEt, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);


    List<OpsTaskGenInfo> dashboardListTaskByMultiLeafDetailProForCount(@Param("nowTime") Date time,@Param("fullTimeSt") Date fullTimeSt,@Param("fullTimeEt") Date fullTimeEt, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> dashboardListTaskByMultiLeafDetailProByLeader(@Param("nowTime") Date time,@Param("fullTimeSt") Date fullTimeSt,@Param("fullTimeEt") Date fullTimeEt, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> dashboardListTaskByMultiLeafDetailProByLeaderForCount(@Param("nowTime") Date time,@Param("fullTimeSt") Date fullTimeSt,@Param("fullTimeEt") Date fullTimeEt, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);


    List<OpsTaskGenInfo> dashboardListTaskByMultiLeafDetailByLeader(@Param("nowTime") Date time, @Param("genTime") String genTime, @Param("dc") ConditionTaskDTO condition);


    List<Long> dashboardListTaskCount(@Param("ew") Wrapper<OpsTaskGenInfo> wrapper, @Param("dc") ConditionTaskDTO condition);

    long taskGenInfoFindLeafAllStatus(@Param("taskId") String taskId, @Param("parentId") Long parentId);

    List<OpsTaskGenInfo> findChildDetailByAuth(@Param("ew") LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo>  findChildDetailForStatic(@Param("ew") LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper, @Param("dc") ConditionTaskDTO condition);
    List<OpsTaskGenInfo> listCustomAudit(@Param("ew") LambdaQueryWrapper<OpsTaskGenInfo> wrapper, @Param("dc") ConditionTaskDTO condition);

    void realDeleted(@Param("dateStr") String format);

    List<OpsTaskGenInfo> allTaskForNowDay(@Param("date") String date);

    List<OpsTaskGenInfo> allTaskForNowDayV1(@Param("date") String date);

    void updateSingleTaskOperationInfo(@Param("id") Long id, @Param("userId") String userId);

    void updateBatchTaskOperationInfo(@Param("ids") List<Long> hitIds, @Param("userId") String userId);

    void resetOperationId(@Param("ids") List<Long> userIds);

    void resetOperationCheckId(@Param("ids") List<Long> infoIds);

    void updateOperationCheckInfo(@Param("ids") List<Long> ids, @Param("userId") String userId);

    List<OpsTaskGenInfo> checkTaskForUser(@Param("date") String date, @Param("userId") String userId);

    /**
     * 统计指标详情列表相关
     *
     * @param date
     * @param userId
     * @return
     */
    List<OpsTaskGenInfo> checkTaskForUserDetail(@Param("date") String date, @Param("userId") String userId);

    List<OpsTaskGenInfo> checkTaskForLeader(@Param("genTime") String genTime, @Param("cn") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> checkTaskForLeaderByLevelHi(@Param("genTime") String genTime, @Param("cn") ConditionTaskDTO condition);


    /**
     * 统计指标详情列表相关
     *
     * @param genTime
     * @param condition
     * @return
     */
    List<OpsTaskGenInfo> checkTaskForLeaderDetail(@Param("genTime") String genTime, @Param("cn") ConditionTaskDTO condition);

    /**
     * 通过子id获取父节点数据
     *
     * @param ids
     * @return
     */
    List<OpsTaskGenInfo> getParentListByChildIds(@Param("ids") List<Long> ids);

    List<OpsTaskGenInfo> dashboardTempListTaskLeaf(@Param("ew") Wrapper<OpsTaskGenInfo> opsTaskGenInfoWrapper, @Param("dc") ConditionTaskDTO condition);

    List<OpsTaskGenInfo> listByTemplateIdAndNowDate(@Param("tempId") String templateId, @Param("now") String nowDate);

    List<OpsTaskGenInfo> dashboardListTaskByMultiByLeader(@Param("date") Date now, @Param("genTime") String genTime,@Param("dc") ConditionTaskDTO condition);

    //dashboardListParentId
    List<OpsTaskGenInfo> dashboardListTaskByMultiPro(@Param("date")Date nowTime, @Param("genTime")String genTime, @Param("dc")ConditionTaskDTO condition, @Param("nowSt")Date nowSt, @Param("nowEt") Date nowEt);

    List<OpsTaskGenInfo> dashboardListParentId(@Param("date")Date nowTime, @Param("genTime")String genTime, @Param("dc")ConditionTaskDTO condition, @Param("nowSt")Date nowSt, @Param("nowEt") Date nowEt);

    List<OpsTaskGenInfo> dashboardListTaskByMultiByLeaderPro(@Param("date")Date nowTime, @Param("genTime")String genTime, @Param("dc")ConditionTaskDTO condition, @Param("nowSt")Date nowSt, @Param("nowEt") Date nowEt);

    List<WorkAmountByUserVO> getWorkAmountByUser(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<OpsTaskGenInfo> getTaskCompleteDesc(@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    List<OpsTaskGenInfo> getAmountTaskByUser(@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime,@Param("userId") String userId,@Param("orgIds") List<String> orgId);

}




