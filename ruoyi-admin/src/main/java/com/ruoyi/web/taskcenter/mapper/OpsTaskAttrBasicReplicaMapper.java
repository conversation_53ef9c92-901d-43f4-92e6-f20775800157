package com.ruoyi.web.taskcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.taskcenter.domain.OpsSpecThirdInfo;
import com.ruoyi.web.taskcenter.domain.OpsTaskAttrBasicReplica;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【ops_task_attr_basic_replica】的数据库操作Mapper
* @createDate 2024-07-10 20:11:30
* @Entity generator.domain.OpsTaskAttrBasicReplica
*/
@Mapper
public interface OpsTaskAttrBasicReplicaMapper extends BaseMapper<OpsTaskAttrBasicReplica> {

    List<OpsTaskAttrBasicReplica> viewListByTemplateId(@Param("id") String id);

    List<OpsTaskAttrBasicReplica> queryListByTemplateId(@Param("id") String templateId);

    List<OpsTaskAttrBasicReplica> queryListByImportStatus(@Param("orgId") String orgId);


    List<OpsTaskAttrBasicReplica> queryListByImportStatusByPriority(@Param("orgId") String orgId);

    List<OpsSpecThirdInfo> queryUnProcessDataByUserId(@Param("userId") String userId);

    List<OpsTaskAttrBasicReplica> queryListHaveImportByTemplateId(@Param("ls") List<String> templateIdList);

    String queryIndicatorByTriggerId(@Param("id") String triggerId);

    void insertScriptTempTable(@Param("id") Long id, @Param("inId") String indicatorId, @Param("dataId") String dataId, @Param("nowVal") String nowVal,@Param("time") Date date);

    OpsSpecThirdInfo querySingleThirdInfo(@Param("dataId") String dataId);

    List<OpsTaskAttrBasicReplica> queryListByTemplateIdAndUserIdWithOwner(@Param("ids") List<String> templateIds, @Param("userId") String fromId);

    List<Map<String, Object>> getAllFundCount(@Param("year") String year, @Param("month") String month);

    List<OpsTaskAttrBasicReplica> queryListByTemplateIdOfCreateType1();

    /**
     * 通过ids获取本身以及父信息列表
     *
     * @param ids
     * @return
     */
    List<OpsTaskAttrBasicReplica> queryParentAndSelfListBySelfIds(@Param("ids") List<String> ids);
}




