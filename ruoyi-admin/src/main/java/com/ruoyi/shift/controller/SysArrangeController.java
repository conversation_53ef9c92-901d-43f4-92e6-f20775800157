package com.ruoyi.shift.controller;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shift.domain.dto.OpsSysArrangeDTO;
import com.ruoyi.shift.domain.entity.OpsSysArrange;
import com.ruoyi.shift.domain.vo.OpsSysArrangeVO;
import com.ruoyi.shift.service.OpsSysArrangeService;
import com.ruoyi.web.taskcenter.util.AjaxResult;
import lombok.RequiredArgsConstructor;
import oracle.jdbc.proxy.annotation.Post;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/sysArrange")
@RequiredArgsConstructor
public class SysArrangeController {

    private final OpsSysArrangeService opsSysArrangeService;

    /**
     * 获取排班列表
     */
    @GetMapping("/list")
    public AjaxResult list(
        @RequestParam(value = "id") String id,
        @RequestParam(value = "startDate") String startDate,
        @RequestParam(value = "endDate") String endDate
    ) {
        return AjaxResult.success(opsSysArrangeService.list(id, startDate, endDate));
    }

    /**
     * 进行排班
     */
    @PostMapping("/save")
    public AjaxResult save(@RequestBody OpsSysArrange opsSysArrange) {
        Integer count = opsSysArrangeService.findByOrgShiftUserDate(opsSysArrange);
        if (count > 0) {
            return AjaxResult.success("该岗位的该用户的该班次在该天已有排班记录");
        }
        opsSysArrangeService.save(opsSysArrange);
        return AjaxResult.success("操作成功");
    }

    /**
     * 取消排班
     */
    @GetMapping("/del")
    public AjaxResult del(@RequestParam("id") String id) {
        opsSysArrangeService.removeById(id);
        return AjaxResult.success("操作成功");
    }

    /**
     * 班次覆盖
     */
    @PostMapping("/cover")
    @Transactional
    public AjaxResult cover(@RequestBody OpsSysArrangeDTO opsSysArrange) {
        opsSysArrangeService.cover(opsSysArrange);
        return AjaxResult.success("操作成功");
    }

    /**
     * 批量排班
     */
    @PostMapping("/batchScheduling")
    public AjaxResult batchScheduling(@RequestBody OpsSysArrangeDTO opsSysArrange) {
        opsSysArrangeService.batchScheduling(opsSysArrange);
        return AjaxResult.success("操作成功");
    }

    /**
     * 值班信息接口 - 根据当前用户id 查询是否有值班信息。默认查询本周(T日)   ，入参一个 当前日期  ,查询本周内个人值班信息
     */
    @GetMapping("/findByUserDate")
    public AjaxResult findByUserDate(@RequestParam(value = "date") String date) {
        List<OpsSysArrangeVO> list = opsSysArrangeService.findByUserDate(date);
        return AjaxResult.success(list);
    }

    /**
     * 进行交接班
     */
    @PostMapping("/changeShift")
    public AjaxResult changeShift(@RequestBody OpsSysArrange opsSysArrange) {
        opsSysArrange.setChangeDate(DateUtils.getTime());
        opsSysArrangeService.updateById(opsSysArrange);
        return AjaxResult.success("操作成功");
    }

    /**
     * 获取昨日及今日的交接班信息
     */
    @GetMapping("/getChangeShift")
    public AjaxResult getChangeShift(@RequestParam(required = false, value = "date") String date) {
        Map<String, Object> object = opsSysArrangeService.getChangeShift(date);
        return AjaxResult.success(object);
    }

}








