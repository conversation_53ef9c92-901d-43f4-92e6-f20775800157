package com.ruoyi.shift.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.shift.domain.dto.OpsSysShiftDTO;
import com.ruoyi.shift.domain.entity.OpsSysShift;
import com.ruoyi.shift.service.OpsSysArrangeService;
import com.ruoyi.shift.service.OpsSysShiftService;
import com.ruoyi.web.taskcenter.util.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/sysShift")
public class SysShiftController {

    @Autowired
    private OpsSysShiftService opsSysShiftService;

    @Autowired
    private OpsSysArrangeService opsSysArrangeService;

    /**
     * 获取班次列表
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody OpsSysShiftDTO opsSysOrg) {
        IPage<OpsSysShift> list = opsSysShiftService.list(opsSysOrg);
        return AjaxResult.success(list);
    }

    /**
     * 添加班次
     */
    @PostMapping("/save")
    public AjaxResult save(@RequestBody OpsSysShift opsSysShift) {
        //判断班次名称是否重复
        if (opsSysShiftService.checkName(opsSysShift) > 0) {
            return AjaxResult.error("班次名称不可重复");
        }
        opsSysShiftService.save(opsSysShift);
        return AjaxResult.success("添加成功");
    }

    /**
     * 修改班次
     */
    @PostMapping("/update")
    public AjaxResult update(@RequestBody OpsSysShift opsSysShift) {
        //判断班次名称是否重复,排除自己
        if (opsSysShiftService.checkName(opsSysShift) > 0) {
            return AjaxResult.error("班次名称不可重复");
        }
        opsSysShiftService.updateById(opsSysShift);
        return AjaxResult.success("修改成功");
    }

    /**
     * 删除班次
     */
    @GetMapping("/del")
    public AjaxResult del(@RequestParam("id") String id) {
        if (opsSysArrangeService.findByShiftId(id) > 0) {
            return AjaxResult.success(false);
        }
        opsSysShiftService.removeById(id);
        return AjaxResult.success(true);
    }

    /**
     * 通过ID获取班次
     */
    @GetMapping("/getById")
    public AjaxResult getById(@RequestParam("id") String id) {
        return AjaxResult.success(opsSysShiftService.getById(id));
    }

    /**
     * 通过岗位获取班次列表
     */
    @GetMapping("/getByOrgId")
    public AjaxResult getByOrgId(@RequestParam("id") String id) {
        return AjaxResult.success(opsSysShiftService.getByOrgId(id));
    }
}
