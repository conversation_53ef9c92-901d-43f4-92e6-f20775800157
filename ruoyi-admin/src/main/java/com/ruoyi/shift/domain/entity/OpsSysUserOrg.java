package com.ruoyi.shift.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户与机构岗位关联表
 * @TableName ops_sys_user_post
 */
@TableName(value ="ops_sys_user_org")
@Data
public class OpsSysUserOrg implements Serializable {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 岗位ID
     */
    private Long orgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public Long getId() {
        return this.userId;
    }
}
