package com.ruoyi.shift.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员交接表
 * @TableName OPS_TASK_SHIFT_CONF
 */
@TableName(value ="OPS_TASK_SHIFT_CONF")
@Data
public class OpsTaskShiftConf implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 来源用户
     */
    private String fromUser;

    private String fromId;

    private String toId;

    /**
     * 替班用户
     */
    private String toUser;

    /**
     * 失效时间
     */
    private String expireDay;

    /**
     *
     */
    private Date createTime;

    /**
     * 是否有效
     */
    @TableLogic
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
