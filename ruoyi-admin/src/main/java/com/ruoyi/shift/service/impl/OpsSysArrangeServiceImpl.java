package com.ruoyi.shift.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shift.domain.dto.OpsSysArrangeDTO;
import com.ruoyi.shift.domain.entity.OpsSysArrange;
import com.ruoyi.shift.domain.entity.OpsSysShift;
import com.ruoyi.shift.domain.vo.ArrangeUserVO;
import com.ruoyi.shift.domain.vo.ArrangeVO;
import com.ruoyi.shift.domain.vo.OpsSysArrangeVO;
import com.ruoyi.shift.mapper.OpsSysArrangeMapper;
import com.ruoyi.shift.service.OpsSysArrangeService;
import com.ruoyi.shift.service.OpsSysCalendarService;
import com.ruoyi.shift.service.OpsSysShiftService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_attr_basic_replica】的数据库操作Service实现
 * @createDate 2024-07-10 20:11:30
 */
@Service
@AllArgsConstructor
public class OpsSysArrangeServiceImpl extends ServiceImpl<OpsSysArrangeMapper, OpsSysArrange>
    implements OpsSysArrangeService {

    private final OpsSysShiftService opsSysShiftService;

    private final OpsSysCalendarService opsSysCalendarService;

    @Override
    public List<Map<String, Object>> list(String id, String startDate, String endDate) {
        List<OpsSysArrangeVO> list = baseMapper.list(id, startDate, endDate);
        Map<String, String> shiftMap = new HashMap<>();
        for (OpsSysShift opsSysShift : opsSysShiftService.list()) {
            shiftMap.put(opsSysShift.getId(), opsSysShift.getName());
        }
        List<Map<String, Object>> maps = new ArrayList<>();
        List<String> dates = list.stream().map(OpsSysArrange::getDate).distinct().sorted().collect(Collectors.toList());
        for (String date : dates) {
            Map<String, Object> map = new HashMap<>();
            Map<String, List<OpsSysArrangeVO>> listMap = list.stream().filter(x -> date.equals(x.getDate())).collect(Collectors.groupingBy(OpsSysArrangeVO::getShiftId));
            List<ArrangeVO> arrangeVOS = new ArrayList<>();
            for (Map.Entry<String, List<OpsSysArrangeVO>> entry : listMap.entrySet()) {
                ArrangeVO arrangeVO = new ArrangeVO();
                arrangeVO.setShiftId(entry.getKey());
                arrangeVO.setShiftName(shiftMap.get(entry.getKey()));
                List<ArrangeUserVO> arrangeUserVOS = new ArrayList<>();
                for (OpsSysArrangeVO vo : entry.getValue()) {
                    arrangeVO.setSort(vo.getSort());
                    ArrangeUserVO arrangeUserVO = new ArrangeUserVO();
                    arrangeUserVO.setArrangeId(vo.getId());
                    arrangeUserVO.setUserName(vo.getUserName());
                    arrangeUserVO.setUserId(vo.getUserId());
                    arrangeUserVOS.add(arrangeUserVO);
                }
                arrangeVO.setUsers(arrangeUserVOS);
                arrangeVOS.add(arrangeVO);
            }
            arrangeVOS.sort((o1, o2) -> {
                String x = StringUtils.isBlank(o1.getSort()) ? "0" : o1.getSort();
                String y = StringUtils.isBlank(o2.getSort()) ? "0" : o2.getSort();
                return x.compareTo(y);
            });
            map.put("list", arrangeVOS);
            map.put("date", date);
            maps.add(map);
        }
        return maps;
    }

    @Override
    public Integer findByShiftId(String id) {
        return baseMapper.findByShiftId(id);
    }

    @Override
    public void cover(OpsSysArrangeDTO opsSysArrangedDTO) {
        //通过岗位ID、班次ID、日期删除排班记录
        baseMapper.del(opsSysArrangedDTO);
        //添加新的排班记录
        List<OpsSysArrange> list = new ArrayList<>();
        if (StringUtils.isNotBlank(opsSysArrangedDTO.getUserIds())) {
            for (String userId : opsSysArrangedDTO.getUserIds().split(",")) {
                OpsSysArrange opSysArrange = new OpsSysArrange();
                BeanUtils.copyProperties(opsSysArrangedDTO, opSysArrange);
                opSysArrange.setUserId(userId);
                list.add(opSysArrange);
            }
            this.saveBatch(list, 1000);
        }
    }

    @Override
    @Transactional
    public void batchScheduling(OpsSysArrangeDTO opsSysArrangedDTO) {
        // 获取日期列表
        List<String> dateList = opsSysCalendarService.listByDate(opsSysArrangedDTO.getStartDate(), opsSysArrangedDTO.getEndDate());
        if (dateList.isEmpty()) {
            return;
        }
        //清空这个日期区间内这个人的排班信息
        baseMapper.delByDate(opsSysArrangedDTO);
        List<OpsSysArrange> opsSysArranges = dateList.stream().map(date -> {
            OpsSysArrange opsSysArrange = new OpsSysArrange();
            BeanUtils.copyProperties(opsSysArrangedDTO, opsSysArrange);
            opsSysArrange.setDate(date);
            return opsSysArrange;
        }).collect(Collectors.toList());
        //批量排班
        this.saveBatch(opsSysArranges, 1000);
    }

    @Override
    public Integer findByOrgShiftUserDate(OpsSysArrange opsSysArrange) {
        return baseMapper.findByOrgShiftUserDate(opsSysArrange);
    }

    @Override
    public List<OpsSysArrangeVO> findByUserDate(String date) {
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getDate();
        }
        LocalDate localDate = LocalDate.parse(date);
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        LocalDate weekStart = localDate.minusDays(dayOfWeek.getValue() - DayOfWeek.MONDAY.getValue());
        LocalDate weekEnd = weekStart.plusDays(6);
        Long userId = LoginHelper.getUserId();
        return baseMapper.findByUserDate(weekStart, weekEnd, userId);
    }

    @Override
    public List<OpsSysArrangeVO> findByUserDateDurSingle(String date) {
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getDate();
        }
        LocalDate localDate = LocalDate.parse(date);
        Long userId = LoginHelper.getUserId();
        return baseMapper.findByUserDate(localDate, localDate, userId);
    }

    @Override
    public Map<String, Object> getChangeShift(String date) {
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getDate();
        }
        Map<String, Object> result = new HashMap<>(2);
        result.put("today", groupByShift(baseMapper.getChangeShiftToday(date, LoginHelper.getUserId())));
        result.put("lastDay", groupByShift(baseMapper.getChangeShiftLastDay(date)));
        return result;
    }

    /**
     * 按班次名称分组
     */
    private Map<String, List<OpsSysArrangeVO>> groupByShift(List<OpsSysArrangeVO> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(OpsSysArrangeVO::getShiftName));
    }
}
