package com.ruoyi.shift.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.shift.domain.dto.OpsSysCalendarDTO;
import com.ruoyi.shift.domain.entity.OpsSysCalendar;
import com.ruoyi.shift.mapper.OpsSysCalendarMapper;
import com.ruoyi.shift.service.OpsSysCalendarService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:20
 */
@Service
public class OpsSysCalendarServiceImpl extends ServiceImpl<OpsSysCalendarMapper, OpsSysCalendar> implements OpsSysCalendarService {

    private static final String TRADE_N = "N";
    private static final String TRADE_Y = "Y";

    /**
     * 初始化日历
     *
     * @param startYear 开始年度 yyyy
     * @param endYear   结束年度 yyyy
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void init(String startYear, String endYear) {
        // 1、删除数据
        LambdaQueryWrapper<OpsSysCalendar> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.ge(OpsSysCalendar::getCalendarDate, startYear + "-01-01");
        deleteWrapper.le(OpsSysCalendar::getCalendarDate, endYear + "-12-31");
        this.remove(deleteWrapper);

        // 2、生成日历
        LocalDate startDate = LocalDate.of(Integer.parseInt(startYear), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(endYear), 12, 31);
        List<OpsSysCalendar> calendarList = new ArrayList<>(1024);
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            OpsSysCalendar calendar = new OpsSysCalendar();
            calendar.setCalendarDate(date.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            calendar.setTrade(TRADE_N);
            if (date.getDayOfWeek().getValue() <= 5) {
                // 周一到周五,默认为工作日
                calendar.setTrade(TRADE_Y);
            }
            calendar.setYearVal(date.getYear()+"");
            calendar.setMonthVal(date.getMonthValue()+"");
            calendar.setWeekVal(date.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR)+"");
            calendar.setDayWeek(date.getDayOfWeek().getValue()+"");
            calendar.setDateTime(DateUtil.parse(date.getYear()+"-"+date.getMonthValue()+"-"+date.getDayOfMonth()+" 00:00:00","yyyy-MM-dd HH:mm:ss"));
            calendarList.add(calendar);
        }
        if (!calendarList.isEmpty()) {
            this.saveBatch(calendarList);
        }
    }

    /**
     * 更新日历
     *
     * @param dto 节假日的日期
     */
    @Override
    public void updateCalendar(OpsSysCalendarDTO dto) {
        List<String> dateList = new ArrayList<>(64);
        // 元旦
        dateList.addAll(this.between(dto.getNewYearDayStart(), dto.getNewYearDayEnd()));
        // 春节
        dateList.addAll(this.between(dto.getSpringFestivalStart(), dto.getSpringFestivalEnd()));
        // 清明节
        dateList.addAll(this.between(dto.getTombSweepingDayStart(), dto.getTombSweepingDayEnd()));
        // 劳动节
        dateList.addAll(this.between(dto.getLabourDayStart(), dto.getLabourDayEnd()));
        // 端午节
        dateList.addAll(this.between(dto.getDragonBoatFestivalStart(), dto.getDragonBoatFestivalEnd()));
        // 中秋节
        dateList.addAll(this.between(dto.getMidAutumnFestivalStart(), dto.getMidAutumnFestivalEnd()));
        // 国庆节
        dateList.addAll(this.between(dto.getNationalDayStart(), dto.getNationalDayEnd()));
        if (!dateList.isEmpty()) {
            LambdaUpdateWrapper<OpsSysCalendar> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.in(OpsSysCalendar::getCalendarDate, dateList);
            updateWrapper.set(OpsSysCalendar::getTrade, TRADE_N);
            updateWrapper.set(OpsSysCalendar::getHoliday,"1");
            this.update(updateWrapper);
        }
    }

    /**
     * 未来日期的
     *
     * @param taskDeferredCount 延期阈值
     * @param taskEndTime
     * @return 未来的值
     */
    @Override
    public Date queryFeuDate(Integer taskDeferredCount, Date taskEndTime) throws RuntimeException {
        //先取今天的日期
        String today=DateUtil.format(new Date(),"yyyy-MM-dd");
        //再冗余计算未来*4倍的日期
        String moDay=DateUtil.format(DateUtil.offsetDay(new Date(),taskDeferredCount*40),"yyyy-MM-dd");
        OpsSysCalendar tradeDay=baseMapper.selectStringDate(today,moDay,taskDeferredCount);
        if(Objects.isNull(tradeDay)){
            throw new RuntimeException("延期任务执行异常,延期工作日期时间未找到");
        }
        String hourMinute=DateUtil.format(taskEndTime,"HH:mm");
        //返回延期后的时间
        return DateUtil.parse(tradeDay.getCalendarDate()+" "+hourMinute+":00","yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 通过开始时间和结束时间查询中间的交易日期
     * */
    @Override
    public List<String> listByDate(String startDate, String endDate) {
        return baseMapper.listByDate(startDate,endDate);
    }

    @Override
    public String getLastWorkByFixed(String date, String rangeDate) {
        return baseMapper.listRowNumberFixed(rangeDate,date);
    }

    @Override
    public String getNextWorkByFixed(String today, String rangeDate) {
        return baseMapper.nextWorkdayFixed(today,rangeDate);
    }

    @Override
    public String getNextWork(String day, String rangeDate, Integer offset) {
        return baseMapper.nextWorkday(day,rangeDate,offset);
    }

    @Override
    public String getLastWork(String day, String rangeDate, int abs) {
        return baseMapper.listRowNumber(day,rangeDate,abs);
    }

    /**
     * 根据开始结束日期查询中间的日期
     *
     * @param startDateStr 开始时间 yyyy-MM-dd
     * @param endDateStr   结束时间 yyyy-MM-dd
     * @return 返回结果
     */
    private List<String> between(String startDateStr, String endDateStr) {
        if (StrUtil.isEmptyIfStr(startDateStr) || StrUtil.isEmptyIfStr(endDateStr)) {
            return new ArrayList<>();
        }
        if (startDateStr.equals(endDateStr)) {
            return List.of(startDateStr);
        }
        List<String> dateList = new ArrayList<>(8);
        LocalDate startDate = LocalDate.parse(startDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
        LocalDate endDate = LocalDate.parse(endDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            dateList.add(date.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        return dateList;
    }

}
