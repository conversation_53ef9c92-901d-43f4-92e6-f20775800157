package com.ruoyi.shift.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.shift.domain.entity.OpsTaskShiftConf;
import com.ruoyi.shift.domain.entity.SystemUser;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【OPS_TASK_SHIFT_CONF(人员交接表)】的数据库操作Service
* @createDate 2024-11-04 18:50:23
*/
public interface OpsTaskShiftConfService extends IService<OpsTaskShiftConf> {



     List<SystemUser> findUserArrByLeaderId(String userId);


     List<OpsTaskShiftConf>  listByUserType();

     void operationShiftConf(OpsTaskShiftConf conf);

     void resetConf(String id);

}
