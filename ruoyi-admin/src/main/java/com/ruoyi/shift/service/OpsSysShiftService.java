package com.ruoyi.shift.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.shift.domain.dto.OpsSysShiftDTO;
import com.ruoyi.shift.domain.entity.OpsSysShift;

import java.util.List;

public interface OpsSysShiftService extends IService<OpsSysShift> {
    IPage<OpsSysShift> list(OpsSysShiftDTO opsSysOrg);

    List<OpsSysShift> getByOrgId(String id);

    int checkName(OpsSysShift opsSysShift);


}
