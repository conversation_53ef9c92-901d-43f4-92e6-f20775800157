package com.ruoyi.shift.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.shift.domain.dto.OpsSysCalendarDTO;
import com.ruoyi.shift.domain.entity.OpsSysCalendar;
import com.ruoyi.web.taskcenter.util.TaskException;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:20
 */
public interface OpsSysCalendarService extends IService<OpsSysCalendar> {

    /**
     * 初始化日历
     *
     * @param startYear 开始年度 yyyy
     * @param endYear   结束年度 yyyy
     */
    void init(String startYear, String endYear);

    /**
     * 更新日历
     *
     * @param dto 节假日的日期
     */
    void updateCalendar(OpsSysCalendarDTO dto);


    Date queryFeuDate(Integer taskDeferredCount, Date taskEndTime) throws RuntimeException;

    List<String> listByDate(String startDate, String endDate);

    String getLastWorkByFixed(String date, String rangeDate);

    String getNextWorkByFixed(String today, String rangeDate);

    String getNextWork(String day, String rangeDate, Integer offset);

    String getLastWork(String day, String rangeDate, int abs);
}
