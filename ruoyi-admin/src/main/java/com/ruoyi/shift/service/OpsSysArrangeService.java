package com.ruoyi.shift.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.shift.domain.dto.OpsSysArrangeDTO;
import com.ruoyi.shift.domain.entity.OpsSysArrange;
import com.ruoyi.shift.domain.vo.OpsSysArrangeVO;

import java.util.List;
import java.util.Map;

public interface OpsSysArrangeService extends IService<OpsSysArrange> {

    Object list(String id, String startDate,String endDate);

    Integer findByShiftId(String id);

    void cover(OpsSysArrangeDTO opsSysArrange);

    void batchScheduling(OpsSysArrangeDTO opsSysArrange);


    Integer findByOrgShiftUserDate(OpsSysArrange opsSysArrange);


    List<OpsSysArrangeVO> findByUserDate(String date);

    List<OpsSysArrangeVO> findByUserDateDurSingle(String date);

    Map<String, Object> getChangeShift(String date);


}
