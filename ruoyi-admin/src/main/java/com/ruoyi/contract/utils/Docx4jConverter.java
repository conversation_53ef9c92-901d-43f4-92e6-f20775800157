package com.ruoyi.contract.utils;

import lombok.extern.slf4j.Slf4j;
import org.docx4j.Docx4J;
import org.docx4j.convert.out.HTMLSettings;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;

import java.io.*;
import java.nio.charset.StandardCharsets;

@Slf4j
public class Docx4jConverter {
    public static void main(String[] args) {
        try {
            String inputPath = "document.docx";
            String outputPath = "output_docx4j_v11.html";

            // 1. 加载 .docx 文件
            System.out.println("正在加载文件: " + inputPath);
            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(new File(inputPath));
            System.out.println("文件加载成功。");

            // 2. 配置HTML输出设置
            HTMLSettings htmlSettings = new HTMLSettings();

            // 设置图片处理：将图片保存到文件夹并使用相对路径
            // 创建图片文件夹路径，例如 "output_images"
            String imageFolderPath = new File(outputPath).getParent();
            if (imageFolderPath == null) {
                imageFolderPath = ".";
            }
            imageFolderPath += "/output_images";
            new File(imageFolderPath).mkdirs();

            htmlSettings.setImageDirPath(imageFolderPath); // 图片保存的物理路径
            htmlSettings.setImageTargetUri("output_images"); // HTML中img标签的src前缀
            htmlSettings.setWmlPackage(wordMLPackage);

            // 3. 执行转换
            System.out.println("开始转换为HTML...");
            try (OutputStream os = new FileOutputStream(outputPath)) {
                // 使用 Docx4J.toHTML 进行转换
                Docx4J.toHTML(htmlSettings, os, Docx4J.FLAG_EXPORT_PREFER_XSL);
            }

            System.out.println("转换成功！HTML文件已保存至: " + outputPath);

        } catch (Exception e) {
            System.err.println("转换过程中发生错误:");
            e.printStackTrace();
        }
    }

    public static String convertToHtml(byte[] docxBytes) {
        try {
            // 1. 加载 .docx 文件
            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(new ByteArrayInputStream(docxBytes));

            // 2. 配置HTML输出设置
            HTMLSettings htmlSettings = new HTMLSettings();
            htmlSettings.setWmlPackage(wordMLPackage);

            // 3. 执行转换
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            Docx4J.toHTML(htmlSettings, os, Docx4J.FLAG_EXPORT_PREFER_XSL);
            return os.toString(StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("转换过程中发生错误: {}", e.getMessage(), e);
            return null;
        }
    }
}
