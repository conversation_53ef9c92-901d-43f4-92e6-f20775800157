package com.ruoyi.contract.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.util.UUID;

/**
 * 对象存储服务
 * 使用HTTP接口与文件服务器交互
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OssService {

    /**
     * 文件服务器基础URL
     */
    private static final String FILE_SERVER_BASE_URL = "http://100.5.0.15:3000";
//    private static final String FILE_SERVER_BASE_URL = "http://10.1.18.9:3000";

    /**
     * 上传接口URL
     */
    private static final String UPLOAD_URL = FILE_SERVER_BASE_URL + "/upload";

    /**
     * 下载接口URL前缀
     */
    private static final String DOWNLOAD_URL_PREFIX = FILE_SERVER_BASE_URL + "/download/";

    private final RestTemplate restTemplate;

    /**
     * 上传文件
     *
     * @param inputStream 文件输入流
     * @param fileName 文件名
     * @param folder 文件夹名称
     * @return 存储路径
     */
    public String uploadFile(InputStream inputStream, String fileName, String folder) {
        try {
            // 生成唯一文件名
            String uniqueFileName = UUID.randomUUID() + "/" + fileName;

            // 构建文件路径
            String filePath = folder + "/" + uniqueFileName;

            // 将输入流转换为临时文件
            File tempFile = File.createTempFile("upload_", "_" + fileName);
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, length);
                }
            }

            // 准备HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new FileSystemResource(tempFile));
            body.add("filepath", filePath);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(UPLOAD_URL, requestEntity, String.class);

            // 清理临时文件
            tempFile.delete();

            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("文件上传成功: {}", filePath);
                return filePath;
            } else {
                throw new RuntimeException("文件上传失败，服务器响应: " + response.getStatusCode());
            }

        } catch (IOException e) {
            log.error("上传文件失败: {}", fileName, e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 下载文件
     *
     * @param storagePath 存储路径
     * @return 文件输入流
     */
    public InputStream downloadFile(String storagePath) {
        try {
            String downloadUrl = DOWNLOAD_URL_PREFIX + storagePath;

            HttpHeaders headers = new HttpHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<byte[]> response = restTemplate.exchange(
                downloadUrl,
                HttpMethod.GET,
                entity,
                byte[].class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                log.info("文件下载成功: {}", storagePath);
                return new ByteArrayInputStream(response.getBody());
            } else {
                throw new RuntimeException("文件下载失败，服务器响应: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("下载文件失败: {}", storagePath, e);
            throw new RuntimeException("文件下载失败", e);
        }
    }

    /**
     * 删除文件
     * 暂时未实现，后续补充
     *
     * @param storagePath 存储路径
     * @return 是否删除成功
     */
    public boolean deleteFile(String storagePath) {
        // TODO: 实现删除文件接口
        log.warn("删除文件功能暂未实现: {}", storagePath);
        return true; // 暂时返回true，避免影响业务流程
    }

    /**
     * 检查文件是否存在
     * 暂时未实现，后续补充
     *
     * @param storagePath 存储路径
     * @return 是否存在
     */
    public boolean fileExists(String storagePath) {
        // TODO: 实现文件存在检查接口
        log.warn("文件存在检查功能暂未实现: {}", storagePath);
        return true; // 暂时返回true，避免影响业务流程
    }
}
