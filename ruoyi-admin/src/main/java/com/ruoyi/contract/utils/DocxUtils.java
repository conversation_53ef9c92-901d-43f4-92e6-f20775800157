package com.ruoyi.contract.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Word文档处理工具类
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
public class DocxUtils {

    /**
     * 占位符正则表达式 - 匹配 {{...}} 格式
     */
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{\\{([^}]+)\\}\\}");

    /**
     * 检测Word文档中的占位符
     *
     * @param inputStream Word文档输入流
     * @return 占位符列表
     */
    public static List<String> detectPlaceholders(InputStream inputStream) {
        Set<String> placeholders = new LinkedHashSet<>();

        try (XWPFDocument document = new XWPFDocument(inputStream)) {
            // 检测段落中的占位符
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                String text = paragraph.getText();
                if (text != null) {
                    extractPlaceholders(text, placeholders);
                }
            }

            // 检测表格中的占位符
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        String text = cell.getText();
                        if (text != null) {
                            extractPlaceholders(text, placeholders);
                        }
                    }
                }
            }

        } catch (IOException e) {
            log.error("检测占位符失败", e);
            throw new RuntimeException("检测占位符失败", e);
        }

        return new ArrayList<>(placeholders);
    }

    /**
     * 从文本中提取占位符
     *
     * @param text 文本内容
     * @param placeholders 占位符集合
     */
    private static void extractPlaceholders(String text, Set<String> placeholders) {
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(text);
        while (matcher.find()) {
            placeholders.add(matcher.group(1)); // 获取 ${} 中的内容
        }
    }

    /**
     * 创建包含指定内容的简单Word文档
     *
     * @param content 文档内容
     * @return Word文档输入流
     */
    public static InputStream createSimpleDocx(String content) {
        try {
            XWPFDocument document = new XWPFDocument();

            // 创建段落
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText(content != null ? content : "");

            // 转换为输入流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            document.close();

            return new ByteArrayInputStream(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("创建Word文档失败", e);
            throw new RuntimeException("创建Word文档失败", e);
        }
    }

    /**
     * 将输入流转换为字节数组
     *
     * @param inputStream 输入流
     * @return 字节数组
     */
    public static byte[] inputStreamToBytes(InputStream inputStream) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("输入流转换失败", e);
            throw new RuntimeException("输入流转换失败", e);
        }
    }

    /**
     * 字节数组转换为输入流
     *
     * @param bytes 字节数组
     * @return 输入流
     */
    public static InputStream bytesToInputStream(byte[] bytes) {
        return new ByteArrayInputStream(bytes);
    }
}
