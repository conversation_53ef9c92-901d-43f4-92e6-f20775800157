package com.ruoyi.contract.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.contract.domain.dto.InitiateDTO;
import com.ruoyi.contract.service.ContractGenerationService;
import com.ruoyi.web.taskcenter.util.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 合同发起与生成Controller
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Api(tags = "合同发起与生成")
@Slf4j
@RestController
@RequestMapping("/api/contracts")
@RequiredArgsConstructor
public class ContractGenerationController extends BaseController {

    private final ContractGenerationService contractGenerationService;

    /**
     * 发起合同（生成文件并保存）
     */
    @ApiOperation("发起合同（生成文件并保存）")
    @PostMapping("/initiate")
    public AjaxResult initiateContract(@ApiParam("发起请求") @RequestBody @Validated InitiateDTO dto) {
        try {
            Long contractId = contractGenerationService.initiateContract(dto);
            return AjaxResult.success("合同生成并保存成功", contractId);

        } catch (Exception e) {
            log.error("合同生成失败", e);
            return AjaxResult.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 查询合同列表
     */
    @ApiOperation("查询合同列表")
    @GetMapping("/list")
    public AjaxResult getContractList(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("合同名称") @RequestParam(required = false) String contractName,
            @ApiParam("产品名称") @RequestParam(required = false) String productName,
            @ApiParam("产品代码") @RequestParam(required = false) String productCode,
            @ApiParam("状态") @RequestParam(required = false) String status) {
        try {
            IPage<Map<String, Object>> result =
                contractGenerationService.getContractList(current, size, contractName, productName, productCode, status);
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("查询合同列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据合同ID查询所有版本列表
     */
    @ApiOperation("根据合同ID查询所有版本列表")
    @GetMapping("/{contractId}/versions")
    public AjaxResult getContractVersions(@ApiParam("合同ID") @PathVariable Long contractId) {
        try {
            List<Map<String, Object>> versions = contractGenerationService.getContractVersions(contractId);
            return AjaxResult.success(versions);

        } catch (Exception e) {
            log.error("查询合同版本列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除合同
     */
    @ApiOperation("删除合同")
    @DeleteMapping("/{contractId}")
    public AjaxResult deleteContract(@ApiParam("合同ID") @PathVariable Long contractId) {
        try {
            contractGenerationService.deleteContract(contractId);
            return AjaxResult.success("合同删除成功");

        } catch (Exception e) {
            log.error("删除合同失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }



    /**
     * 更新合同状态
     */
    @ApiOperation("更新合同状态")
    @PutMapping("/{contractId}/status")
    public AjaxResult updateContractStatus(
            @ApiParam("合同ID") @PathVariable Long contractId,
            @ApiParam("新状态") @RequestParam String status) {

        try {
            contractGenerationService.updateContractStatus(contractId, status);
            return AjaxResult.success("状态更新成功");

        } catch (Exception e) {
            log.error("状态更新失败", e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取合同详情
     */
    @ApiOperation("获取合同详情")
    @GetMapping("/{contractId}")
    public AjaxResult getContractDetail(@ApiParam("合同ID") @PathVariable Long contractId) {
        try {
            var contract = contractGenerationService.getById(contractId);
            if (contract == null) {
                return AjaxResult.error("合同不存在");
            }
            return AjaxResult.success(contract);

        } catch (Exception e) {
            log.error("获取合同详情失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }
}
