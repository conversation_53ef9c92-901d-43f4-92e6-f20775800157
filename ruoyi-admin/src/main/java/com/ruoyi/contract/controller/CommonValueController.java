package com.ruoyi.contract.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.contract.domain.dto.CommonValueDTO;
import com.ruoyi.contract.domain.vo.CommonValueVO;
import com.ruoyi.contract.service.CommonValueService;
import com.ruoyi.web.taskcenter.util.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公共值管理Controller
 *
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Api(tags = "公共值管理")
@Slf4j
@RestController
@RequestMapping("/api/common-values")
@RequiredArgsConstructor
public class CommonValueController extends BaseController {

    private final CommonValueService commonValueService;

    /**
     * 分页查询公共值列表
     */
    @ApiOperation("分页查询公共值列表")
    @GetMapping("/page")
    public AjaxResult getCommonValuePage(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("产品类型") @RequestParam(required = false) String productType,
            @ApiParam("模板分类") @RequestParam(required = false) String templateCategory,
            @ApiParam("公共值名称") @RequestParam(required = false) String name) {
        try {
            IPage<CommonValueVO> page = commonValueService.getCommonValuePage(current, size, productType, templateCategory, name);
            return AjaxResult.success(page);

        } catch (Exception e) {
            log.error("查询公共值列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据产品类型和模板分类查询公共值列表
     */
    @ApiOperation("根据产品类型和模板分类查询公共值列表")
    @GetMapping("/query")
    public AjaxResult getCommonValuesByTypeAndCategory(
            @ApiParam("产品类型") @RequestParam(required = false) String productType,
            @ApiParam("模板分类") @RequestParam(required = false) String templateCategory) {
        try {
            List<CommonValueVO> commonValues = commonValueService.getCommonValuesByTypeAndCategory(productType, templateCategory);
            return AjaxResult.success(commonValues);

        } catch (Exception e) {
            log.error("查询公共值列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共值详情
     */
    @ApiOperation("获取公共值详情")
    @GetMapping("/{id}")
    public AjaxResult getCommonValueById(@ApiParam("公共值ID") @PathVariable Long id) {
        try {
            CommonValueVO commonValue = commonValueService.getCommonValueById(id);
            return AjaxResult.success(commonValue);

        } catch (Exception e) {
            log.error("获取公共值详情失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 新增公共值
     */
    @ApiOperation("新增公共值")
    @PostMapping
    public AjaxResult saveCommonValue(@ApiParam("公共值信息") @RequestBody @Validated CommonValueDTO dto) {
        try {
            commonValueService.saveCommonValue(dto);
            return AjaxResult.success("保存成功");

        } catch (Exception e) {
            log.error("保存公共值失败", e);
            return AjaxResult.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 更新公共值
     */
    @ApiOperation("更新公共值")
    @PutMapping("/{id}")
    public AjaxResult updateCommonValue(
            @ApiParam("公共值ID") @PathVariable Long id,
            @ApiParam("公共值信息") @RequestBody @Validated CommonValueDTO dto) {
        try {
            dto.setId(id);
            commonValueService.updateCommonValue(dto);
            return AjaxResult.success("更新成功");

        } catch (Exception e) {
            log.error("更新公共值失败", e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除公共值
     */
    @ApiOperation("删除公共值")
    @DeleteMapping("/{id}")
    public AjaxResult deleteCommonValue(@ApiParam("公共值ID") @PathVariable Long id) {
        try {
            commonValueService.deleteCommonValue(id);
            return AjaxResult.success("删除成功");

        } catch (Exception e) {
            log.error("删除公共值失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }
}
