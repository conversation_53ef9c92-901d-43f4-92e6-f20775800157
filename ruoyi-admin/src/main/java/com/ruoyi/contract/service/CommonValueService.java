package com.ruoyi.contract.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.contract.domain.CommonValue;
import com.ruoyi.contract.domain.dto.CommonValueDTO;
import com.ruoyi.contract.domain.vo.CommonValueVO;
import com.ruoyi.contract.mapper.CommonValueMapper;
import com.ruoyi.contract.utils.OssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 公共值服务
 *
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonValueService extends ServiceImpl<CommonValueMapper, CommonValue> {

    private final OssService ossService;

    /**
     * 分页查询公共值列表
     *
     * @param current 当前页
     * @param size 每页大小
     * @param productType 产品类型
     * @param templateCategory 模板分类
     * @param name 公共值名称
     * @return 分页结果
     */
    public IPage<CommonValueVO> getCommonValuePage(Long current, Long size, String productType, String templateCategory, String name) {
        Page<CommonValue> page = new Page<>(current, size);
        
        LambdaQueryWrapper<CommonValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(productType), CommonValue::getProductType, productType)
                   .like(StringUtils.hasText(templateCategory), CommonValue::getTemplateCategory, templateCategory)
                   .like(StringUtils.hasText(name), CommonValue::getName, name)
                   .orderByDesc(CommonValue::getCreatedTime);
        
        IPage<CommonValue> commonValuePage = page(page, queryWrapper);
        
        // 转换为VO
        IPage<CommonValueVO> voPage = new Page<>(current, size, commonValuePage.getTotal());
        List<CommonValueVO> voList = commonValuePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }

    /**
     * 根据产品类型和模板分类查询公共值列表
     *
     * @param productType 产品类型
     * @param templateCategory 模板分类
     * @return 公共值列表
     */
    public List<CommonValueVO> getCommonValuesByTypeAndCategory(String productType, String templateCategory) {
        LambdaQueryWrapper<CommonValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.hasText(productType), CommonValue::getProductType, productType)
                   .eq(StringUtils.hasText(templateCategory), CommonValue::getTemplateCategory, templateCategory)
                   .orderByDesc(CommonValue::getCreatedTime);
        
        List<CommonValue> commonValues = list(queryWrapper);
        return commonValues.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID获取公共值详情
     *
     * @param id 公共值ID
     * @return 公共值详情
     */
    public CommonValueVO getCommonValueById(Long id) {
        CommonValue commonValue = getById(id);
        if (commonValue == null) {
            throw new RuntimeException("公共值不存在");
        }
        return convertToVO(commonValue);
    }

    /**
     * 保存公共值
     *
     * @param dto 公共值DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCommonValue(CommonValueDTO dto) {
        CommonValue commonValue = new CommonValue();
        BeanUtils.copyProperties(dto, commonValue);
        
        save(commonValue);
        log.info("公共值保存成功，名称: {}", dto.getName());
    }

    /**
     * 更新公共值
     *
     * @param dto 公共值DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCommonValue(CommonValueDTO dto) {
        CommonValue existingValue = getById(dto.getId());
        if (existingValue == null) {
            throw new RuntimeException("公共值不存在");
        }

        // 如果是文件类型且文件路径发生变化，删除旧文件
        if (CommonValue.VALUE_TYPE_FILE.equals(existingValue.getValueType())
            && StringUtils.hasText(existingValue.getValueContent())
            && !existingValue.getValueContent().equals(dto.getValueContent())) {
            try {
                ossService.deleteFile(existingValue.getValueContent());
            } catch (Exception e) {
                log.warn("删除旧公共值文件失败: {}", existingValue.getValueContent(), e);
            }
        }

        // 更新字段
        existingValue.setProductType(dto.getProductType());
        existingValue.setTemplateCategory(dto.getTemplateCategory());
        existingValue.setName(dto.getName());
        existingValue.setValueContent(dto.getValueContent());
        existingValue.setValueType(dto.getValueType());
        existingValue.setHtmlContent(dto.getHtmlContent());
        existingValue.setRemarks(dto.getRemarks());

        updateById(existingValue);
        log.info("公共值更新成功，ID: {}", dto.getId());
    }

    /**
     * 删除公共值
     *
     * @param id 公共值ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteCommonValue(Long id) {
        CommonValue commonValue = getById(id);
        if (commonValue == null) {
            throw new RuntimeException("公共值不存在");
        }

        // 如果是文件类型，删除文件
        if (CommonValue.VALUE_TYPE_FILE.equals(commonValue.getValueType())
            && StringUtils.hasText(commonValue.getValueContent())) {
            try {
                ossService.deleteFile(commonValue.getValueContent());
            } catch (Exception e) {
                log.warn("删除公共值文件失败: {}", commonValue.getValueContent(), e);
            }
        }

        removeById(id);
        log.info("公共值删除成功，ID: {}", id);
    }

    /**
     * 转换为VO
     *
     * @param commonValue 公共值实体
     * @return 公共值VO
     */
    private CommonValueVO convertToVO(CommonValue commonValue) {
        CommonValueVO vo = new CommonValueVO();
        BeanUtils.copyProperties(commonValue, vo);
        return vo;
    }
}
