package com.ruoyi.contract.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.contract.domain.ParameterValue;
import com.ruoyi.contract.mapper.ParameterValueMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 参数值服务
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Service
public class ParameterValueService extends ServiceImpl<ParameterValueMapper, ParameterValue> {

    /**
     * 根据参数集ID获取所有参数值
     *
     * @param setId 参数集ID
     * @return 参数值列表
     */
    public List<ParameterValue> getBySetId(Long setId) {
        LambdaQueryWrapper<ParameterValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ParameterValue::getSetId, setId);
        queryWrapper.orderBy(true, true, ParameterValue::getId);
        return list(queryWrapper);
    }

    /**
     * 根据参数集ID和参数名获取参数值
     *
     * @param setId 参数集ID
     * @param paramName 参数名
     * @return 参数值
     */
    public ParameterValue getBySetIdAndParamName(Long setId, String paramName) {
        LambdaQueryWrapper<ParameterValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ParameterValue::getSetId, setId)
                   .eq(ParameterValue::getParamName, paramName);
        return getOne(queryWrapper);
    }

    /**
     * 根据参数集ID删除所有参数值
     *
     * @param setId 参数集ID
     */
    public void deleteBySetId(Long setId) {
        LambdaQueryWrapper<ParameterValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ParameterValue::getSetId, setId);
        remove(queryWrapper);
    }

    /**
     * 根据参数名获取历史值（用于前端弹窗选择）
     *
     * @param paramName 参数名
     * @return 历史值列表
     */
    public List<ParameterValue> getHistoryValuesByParamName(String paramName) {
        LambdaQueryWrapper<ParameterValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ParameterValue::getParamName, paramName)
                   .orderByDesc(ParameterValue::getCreatedTime);
        return list(queryWrapper);
    }

    /**
     * 根据参数名和参数值查询参数记录
     * 用于自动带出参数集逻辑
     *
     * @param paramName 参数名
     * @param paramValue 参数值
     * @return 匹配的参数值记录
     */
    public ParameterValue findByParamNameAndValue(String paramName, String paramValue) {
        LambdaQueryWrapper<ParameterValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ParameterValue::getParamName, paramName)
                   .eq(ParameterValue::getParamValue, paramValue)
                   .orderByDesc(ParameterValue::getCreatedTime)
                   .last("LIMIT 1");
        return getOne(queryWrapper);
    }
}
