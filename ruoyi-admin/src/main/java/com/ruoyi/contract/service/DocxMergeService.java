package com.ruoyi.contract.service;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.Docx4J;
import org.docx4j.TextUtils;
import org.docx4j.jaxb.Context;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.AlternativeFormatInputPart;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.relationships.Relationship;
import org.docx4j.wml.*;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBElement;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高格式要求的Word文档合并服务
 * 混合使用 poi-tl (文本替换) + docx4j AltChunk (文件替换)
 * 增加错误处理和降级方案
 *
 * <AUTHOR> Assistant
 */
@Component
@Slf4j
public class DocxMergeService {

    /**
     * 高格式要求的混合替换 (已修复版本)
     * 新流程：
     * 1. 对主模板进行文本替换。
     * 2. 对每一个子文档进行文本替换。
     * 3. 将处理后的子文档合并到处理后的主模板中。
     *
     * @param templateBytes    模板文档字节数组
     * @param textReplacements 文本替换映射 (参数名 -> 文本值)
     * @param fileReplacements 文件替换映射 (占位符 -> 待插入的子文档字节数组)
     * @return 处理后的文档字节数组
     */
    public byte[] highQualityMixedReplace(byte[] templateBytes,
                                          Map<String, String> textReplacements,
                                          Map<String, byte[]> fileReplacements) throws Exception {

        log.info("开始高格式要求的混合替换（新流程），文本替换: {} 项, 文件替换: {} 项",
            textReplacements.size(), fileReplacements.size());

        // 验证输入格式，这是一个很好的实践
        if (!isValidDocxFormat(templateBytes)) {
            log.warn("输入文档格式无效，使用降级方案创建新文档");
            return createNewDocxWithContent(templateBytes, textReplacements);
        }

        // --- 正确的流程开始 ---

        // 步骤 1: 对主模板进行文本替换
        byte[] processedTemplateBytes = templateBytes;
        if (!textReplacements.isEmpty()) {
            try {
                log.info("阶段 1: 对主模板进行文本替换...");
                // 使用您现有的文本替换方法处理主模板
                processedTemplateBytes = apachePoiTextReplace(templateBytes, textReplacements);
                log.info("主模板文本替换完成。");
            } catch (Exception e) {
                // 如果主模板替换失败，记录错误但继续，至少可以尝试文件合并
                log.error("主模板文本替换失败，将使用原始模板继续文件替换: {}", e.getMessage(), e);
            }
        }

        // 步骤 2: 对所有子文档进行文本替换
        Map<String, byte[]> processedFileReplacements = new HashMap<>();
        if (!fileReplacements.isEmpty()) {
            log.info("阶段 2: 对 {} 个子文档进行文本替换...", fileReplacements.size());
            // 遍历每一个需要插入的子文档
            for (Map.Entry<String, byte[]> entry : fileReplacements.entrySet()) {
                String placeholder = entry.getKey();
                byte[] subDocBytes = entry.getValue();
                byte[] processedSubDocBytes = subDocBytes; // 默认使用原始子文档

                // 确保子文档有效，并且有文本需要替换
                if (subDocBytes != null && subDocBytes.length > 0 && textReplacements != null && !textReplacements.isEmpty()) {
                    try {
                        log.debug("正在处理占位符为 '{}' 的子文档...", placeholder);
                        // 对子文档应用同样的文本替换逻辑
                        processedSubDocBytes = apachePoiTextReplace(subDocBytes, textReplacements);
                        log.debug("子文档 '{}' 的文本替换完成。", placeholder);
                    } catch (Exception e) {
                        // 如果某个子文档处理失败，记录警告，并使用原始子文档，不中断整个流程
                        log.warn("子文档 '{}' 文本替换失败，将使用其原始内容进行合并: {}", placeholder, e.getMessage());
                    }
                }
                // 将处理后（或原始）的子文档放入新的Map中
                processedFileReplacements.put(placeholder, processedSubDocBytes);
            }
            log.info("所有子文档文本替换处理完成。");
        }


        // 步骤 3: 使用处理后的主模板和处理后的子文档，进行最终的文件合并
        byte[] finalResult = processedTemplateBytes;
        if (!fileReplacements.isEmpty()) {
            try {
                log.info("阶段 3: 开始文件合并（使用已处理过的文档）...");
                // 调用您现有的文件替换方法，但传入的是已经处理过的文档
                finalResult = docx4jFileReplace(processedTemplateBytes, processedFileReplacements);
                log.info("文件合并完成。");
            } catch (Exception e) {
                log.error("最后的文件合并步骤失败: {}", e.getMessage(), e);
                // 如果文件合并失败，至少返回经过文本替换的主模板，这也是一种有效的降级策略
            }
        }

        log.info("高格式要求的混合替换完成，最终文档大小: {} bytes", finalResult.length);
        return finalResult;
    }


    /**
     * 使用Apache POI进行安全的文本替换
     * 确保生成的文档格式正确，包括页眉页脚处理
     */
    private byte[] apachePoiTextReplace(byte[] templateBytes, Map<String, String> textReplacements) throws Exception {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(templateBytes);
             org.apache.poi.xwpf.usermodel.XWPFDocument document = new org.apache.poi.xwpf.usermodel.XWPFDocument(inputStream)) {

            // 遍历所有段落进行文本替换
            for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : document.getParagraphs()) {
                replaceTextInParagraph(paragraph, textReplacements);
            }

            // 遍历所有表格进行文本替换
            for (org.apache.poi.xwpf.usermodel.XWPFTable table : document.getTables()) {
                for (org.apache.poi.xwpf.usermodel.XWPFTableRow row : table.getRows()) {
                    for (org.apache.poi.xwpf.usermodel.XWPFTableCell cell : row.getTableCells()) {
                        for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : cell.getParagraphs()) {
                            replaceTextInParagraph(paragraph, textReplacements);
                        }
                    }
                }
            }

            // 处理页眉
            replaceTextInHeaders(document, textReplacements);

            // 处理页脚
            replaceTextInFooters(document, textReplacements);

            // 保存到字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);

            log.info("Apache POI文本替换完成（包括页眉页脚），文档大小: {} bytes", outputStream.size());
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("Apache POI文本替换失败: {}", e.getMessage());
            // 如果Apache POI也失败，使用降级方案
            return createNewDocxWithContent(templateBytes, textReplacements);
        }
    }

    /**
     * 处理页眉中的文本替换
     */
    private void replaceTextInHeaders(org.apache.poi.xwpf.usermodel.XWPFDocument document, Map<String, String> textReplacements) {
        try {
            java.util.List<org.apache.poi.xwpf.usermodel.XWPFHeader> headers = document.getHeaderList();
            for (org.apache.poi.xwpf.usermodel.XWPFHeader header : headers) {
                // 处理页眉段落
                for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : header.getParagraphs()) {
                    replaceTextInParagraph(paragraph, textReplacements);
                }

                // 处理页眉表格
                for (org.apache.poi.xwpf.usermodel.XWPFTable table : header.getTables()) {
                    for (org.apache.poi.xwpf.usermodel.XWPFTableRow row : table.getRows()) {
                        for (org.apache.poi.xwpf.usermodel.XWPFTableCell cell : row.getTableCells()) {
                            for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceTextInParagraph(paragraph, textReplacements);
                            }
                        }
                    }
                }
            }
            log.debug("页眉文本替换完成，处理了 {} 个页眉", headers.size());
        } catch (Exception e) {
            log.warn("页眉文本替换失败: {}", e.getMessage());
        }
    }

    /**
     * 处理页脚中的文本替换
     */
    private void replaceTextInFooters(org.apache.poi.xwpf.usermodel.XWPFDocument document, Map<String, String> textReplacements) {
        try {
            java.util.List<org.apache.poi.xwpf.usermodel.XWPFFooter> footers = document.getFooterList();
            for (org.apache.poi.xwpf.usermodel.XWPFFooter footer : footers) {
                // 处理页脚段落
                for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : footer.getParagraphs()) {
                    replaceTextInParagraph(paragraph, textReplacements);
                }

                // 处理页脚表格
                for (org.apache.poi.xwpf.usermodel.XWPFTable table : footer.getTables()) {
                    for (org.apache.poi.xwpf.usermodel.XWPFTableRow row : table.getRows()) {
                        for (org.apache.poi.xwpf.usermodel.XWPFTableCell cell : row.getTableCells()) {
                            for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : cell.getParagraphs()) {
                                replaceTextInParagraph(paragraph, textReplacements);
                            }
                        }
                    }
                }
            }
            log.debug("页脚文本替换完成，处理了 {} 个页脚", footers.size());
        } catch (Exception e) {
            log.warn("页脚文本替换失败: {}", e.getMessage());
        }
    }

    /**
     * 在段落中进行文本替换，保留原有格式
     * 支持占位符跨运行块的情况，使用非破坏性替换保持格式完整性
     */
    private void replaceTextInParagraph(org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph, Map<String, String> textReplacements) {
        try {
            // 获取段落中所有的运行块
            List<org.apache.poi.xwpf.usermodel.XWPFRun> runs = paragraph.getRuns();
            if (runs.isEmpty()) {
                return;
            }

            // 合并所有运行块的文本，同时记录格式信息
            StringBuilder fullText = new StringBuilder();
            List<RunInfo> runInfos = new ArrayList<>();

            for (org.apache.poi.xwpf.usermodel.XWPFRun run : runs) {
                String runText = run.getText(0);
                if (runText != null) {
                    RunInfo runInfo = new RunInfo();
                    runInfo.run = run;
                    runInfo.startIndex = fullText.length();
                    runInfo.endIndex = fullText.length() + runText.length();
                    runInfo.text = runText;
                    runInfos.add(runInfo);

                    fullText.append(runText);
                }
            }

            String originalText = fullText.toString();
            if (originalText.trim().isEmpty()) {
                return;
            }

            // 检查是否包含需要替换的占位符
            String replacedText = originalText;
            boolean hasReplacement = false;

            for (Map.Entry<String, String> entry : textReplacements.entrySet()) {
                String placeholder = "{{" + entry.getKey() + "}}";
                if (originalText.contains(placeholder)) {
                    String replacement = entry.getValue() != null ? entry.getValue() : "";
                    replacedText = replacedText.replace(placeholder, replacement);
                    hasReplacement = true;
                    log.debug("在段落中找到占位符: {} -> {}", placeholder, replacement);
                }
            }

            // 如果没有替换，直接返回
            if (!hasReplacement) {
                return;
            }

            // 使用非破坏性替换方法保留格式
            replaceTextNonDestructive(paragraph, runInfos, originalText, replacedText);

            log.debug("替换段落文本并保留格式: {} -> {}", originalText, replacedText);

        } catch (Exception e) {
            log.warn("段落文本替换失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 非破坏性文本替换，保留原始格式结构
     * 只修改包含占位符的运行块，保持其他运行块不变
     */
    private void replaceTextNonDestructive(org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph,
                                          List<RunInfo> runInfos,
                                          String originalText,
                                          String replacedText) {
        try {
            // 如果文本长度相同，尝试直接替换各个运行块中的内容
            if (originalText.length() == replacedText.length()) {
                int offset = 0;
                for (RunInfo runInfo : runInfos) {
                    int runLength = runInfo.text.length();
                    String newRunText = replacedText.substring(offset, offset + runLength);
                    runInfo.run.setText(newRunText, 0);
                    offset += runLength;
                }
                return;
            }

            // 如果长度不同，需要重新分配文本，但尽量保留格式
            redistributeTextPreservingFormat(paragraph, runInfos, replacedText);

        } catch (Exception e) {
            log.warn("非破坏性替换失败，使用降级方案: {}", e.getMessage());
            // 降级方案：使用第一个运行块的格式创建新的运行块
            fallbackReplacement(paragraph, runInfos, replacedText);
        }
    }

    /**
     * 重新分配文本，尽量保留格式结构
     */
    private void redistributeTextPreservingFormat(org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph,
                                                 List<RunInfo> runInfos,
                                                 String newText) {
        // 清空所有运行块的文本，但保留运行块本身
        for (RunInfo runInfo : runInfos) {
            runInfo.run.setText("", 0);
        }

        // 如果新文本为空，直接返回
        if (newText.isEmpty()) {
            return;
        }

        // 找到最适合的运行块来放置新文本（优先选择原来文本最长的）
        RunInfo bestRun = runInfos.get(0);
        for (RunInfo runInfo : runInfos) {
            if (runInfo.text.length() > bestRun.text.length()) {
                bestRun = runInfo;
            }
        }

        // 将所有新文本放入最佳运行块
        bestRun.run.setText(newText, 0);
    }

    /**
     * 降级替换方案：保留主要格式但可能丢失部分格式细节
     */
    private void fallbackReplacement(org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph,
                                   List<RunInfo> runInfos,
                                   String replacedText) {
        // 找到最佳的格式运行块
        org.apache.poi.xwpf.usermodel.XWPFRun bestFormatRun = findBestFormatRunFromList(runInfos);

        // 清除所有运行块
        for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
            paragraph.removeRun(i);
        }

        // 创建新的运行块，继承最佳格式
        org.apache.poi.xwpf.usermodel.XWPFRun newRun = paragraph.createRun();
        if (bestFormatRun != null) {
            copyRunFormat(bestFormatRun, newRun);
        }
        newRun.setText(replacedText);
    }

    /**
     * 从运行块信息列表中找到最佳格式的运行块
     */
    private org.apache.poi.xwpf.usermodel.XWPFRun findBestFormatRunFromList(List<RunInfo> runInfos) {
        // 优先选择有重要格式的运行块
        for (RunInfo runInfo : runInfos) {
            if (hasSignificantFormatting(runInfo.run)) {
                return runInfo.run;
            }
        }

        // 如果都没有重要格式，返回第一个
        return runInfos.isEmpty() ? null : runInfos.get(0).run;
    }

    /**
     * 运行块信息类
     */
    private static class RunInfo {
        org.apache.poi.xwpf.usermodel.XWPFRun run;
        int startIndex;
        int endIndex;
        String text;
    }

    /**
     * 查找最佳格式的运行块（优先选择包含占位符的运行块）
     */
    private org.apache.poi.xwpf.usermodel.XWPFRun findBestFormatRun(List<RunInfo> runInfos, java.util.Set<String> placeholders) {
        // 优先查找完全包含占位符的运行块
        for (RunInfo runInfo : runInfos) {
            for (String placeholder : placeholders) {
                if (runInfo.text.contains(placeholder)) {
                    return runInfo.run;
                }
            }
        }

        // 查找包含占位符起始部分的运行块
        for (RunInfo runInfo : runInfos) {
            for (String placeholder : placeholders) {
                if (runInfo.text.contains("{{") || runInfo.text.contains("}}")) {
                    return runInfo.run;
                }
            }
        }

        // 如果都没找到，返回第一个有格式的运行块
        for (RunInfo runInfo : runInfos) {
            if (hasSignificantFormatting(runInfo.run)) {
                return runInfo.run;
            }
        }

        return null;
    }

    /**
     * 检查运行块是否有重要的格式
     */
    private boolean hasSignificantFormatting(org.apache.poi.xwpf.usermodel.XWPFRun run) {
        try {
            return run.isBold() ||
                   run.isItalic() ||
                   run.getUnderline() != null ||
                   run.getColor() != null ||
                   run.getFontSize() > 12 ||
                   (run.getFontFamily() != null && !run.getFontFamily().equals("Calibri"));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 复制运行块的格式属性
     */
    private void copyRunFormat(org.apache.poi.xwpf.usermodel.XWPFRun sourceRun, org.apache.poi.xwpf.usermodel.XWPFRun targetRun) {
        try {
            // 复制字体属性
            if (sourceRun.getFontFamily() != null) {
                targetRun.setFontFamily(sourceRun.getFontFamily());
            }

            // 复制字体大小
            try {
                int fontSize = sourceRun.getFontSize();
                if (fontSize != -1) {
                    targetRun.setFontSize(fontSize);
                }
            } catch (Exception e) {
                // 忽略字体大小设置错误
            }

            // 复制字体样式
            targetRun.setBold(sourceRun.isBold());
            targetRun.setItalic(sourceRun.isItalic());

            // 复制下划线
            if (sourceRun.getUnderline() != null) {
                targetRun.setUnderline(sourceRun.getUnderline());
            }

            // 复制字体颜色
            if (sourceRun.getColor() != null && !sourceRun.getColor().isEmpty()) {
                targetRun.setColor(sourceRun.getColor());
            }

            // 复制删除线
            targetRun.setStrikeThrough(sourceRun.isStrikeThrough());

            // 尝试复制其他可用的格式属性
            try {
                // 复制高亮颜色（如果支持）
                org.openxmlformats.schemas.wordprocessingml.x2006.main.STHighlightColor.Enum highlight = sourceRun.getTextHighlightColor();
                if (highlight != null) {
                    targetRun.setTextHighlightColor(highlight.toString());
                }
            } catch (Exception e) {
                // 忽略不支持的属性
            }

            log.debug("复制运行块格式: 字体={}, 粗体={}, 斜体={}",
                    sourceRun.getFontFamily(), sourceRun.isBold(), sourceRun.isItalic());

        } catch (Exception e) {
            log.warn("复制运行块格式失败: {}", e.getMessage());
        }
    }

    /**
     * 使用 poi-tl 进行文本替换 (保持模板格式)
     * 增加错误处理和配置优化
     */
    private byte[] poiTlTextReplace(byte[] templateBytes, Map<String, String> textReplacements) throws Exception {
        try (ByteArrayInputStream templateStream = new ByteArrayInputStream(templateBytes);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建安全的配置，禁用严格模式
            Configure config = Configure.builder()
                .useSpringEL(false)  // 禁用Spring EL表达式
                .build();

            // 创建模板引擎
            XWPFTemplate template = XWPFTemplate.compile(templateStream, config);

            // 执行文本替换
            template.render(textReplacements);

            // 写入输出流
            template.write(outputStream);
            template.close();

            byte[] result = outputStream.toByteArray();

            // 验证结果是否有效
            if (result.length == 0) {
                throw new RuntimeException("poi-tl 生成的文档为空");
            }

            return result;
        } catch (Exception e) {
            log.error("poi-tl 文本替换失败: {}", e.getMessage(), e);

            // 如果 poi-tl 失败，尝试降级到基础的文本替换
            log.warn("尝试使用基础文本替换方法");
            return fallbackTextReplace(templateBytes, textReplacements);
        }
    }

    /**
     * 降级文本替换方法
     * 当 poi-tl 失败时使用的备用方案
     * 如果输入不是docx格式，则创建一个新的docx文档
     */
    private byte[] fallbackTextReplace(byte[] templateBytes, Map<String, String> textReplacements) throws Exception {
        try {
            // 首先尝试作为docx文档处理
            if (isValidDocxFormat(templateBytes)) {
                // 如果是有效的docx格式，使用字符串替换
                return simpleDocxTextReplace(templateBytes, textReplacements);
            } else {
                // 如果不是docx格式，创建新的docx文档
                return createNewDocxWithContent(templateBytes, textReplacements);
            }
        } catch (Exception e) {
            log.error("降级文本替换也失败: {}", e.getMessage());
            // 最后的备用方案：返回原始内容
            return templateBytes;
        }
    }

    /**
     * 简单的docx文档文本替换
     */
    private byte[] simpleDocxTextReplace(byte[] docxBytes, Map<String, String> textReplacements) throws Exception {
        // 将docx内容转换为字符串进行替换（这是一个简化的方法）
        String content = new String(docxBytes, "UTF-8");

        for (Map.Entry<String, String> entry : textReplacements.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            String replacement = entry.getValue() != null ? entry.getValue() : "";
            content = content.replace(placeholder, replacement);
        }

        return content.getBytes("UTF-8");
    }

    /**
     * 创建新的docx文档
     */
    private byte[] createNewDocxWithContent(byte[] originalBytes, Map<String, String> textReplacements) throws Exception {
        // 将原始内容转换为字符串
        String content = new String(originalBytes, "UTF-8");

        // 进行文本替换
        for (Map.Entry<String, String> entry : textReplacements.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            String replacement = entry.getValue() != null ? entry.getValue() : "";
            content = content.replace(placeholder, replacement);
        }

        // 使用Apache POI创建新的docx文档
        try (org.apache.poi.xwpf.usermodel.XWPFDocument document = new org.apache.poi.xwpf.usermodel.XWPFDocument()) {
            // 分段处理内容
            String[] lines = content.split("\n");
            for (String line : lines) {
                org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph = document.createParagraph();
                org.apache.poi.xwpf.usermodel.XWPFRun run = paragraph.createRun();
                run.setText(line);
            }

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);

            log.info("创建新的docx文档，大小: {} bytes", outputStream.size());
            return outputStream.toByteArray();
        }
    }

    /**
     * 使用 docx4j AltChunk 进行文件替换 (100% 保持子文档格式)
     * 包可见性，用于测试
     */
    byte[] docx4jFileReplace(byte[] templateBytes, Map<String, byte[]> fileReplacements) throws Exception {
        log.info("docx4jFileReplace 方法已修复，添加了临时文件清理逻辑");
        log.info("输入参数: templateBytes大小={} bytes, fileReplacements数量={}",
            templateBytes.length, fileReplacements.size());

        Path templatePath = null;
        try {
            // 1. 创建主模板的临时文件
            templatePath = Files.createTempFile("template", ".docx");
            Files.write(templatePath, templateBytes);

            for (Map.Entry<String, byte[]> entry : fileReplacements.entrySet()) {
                String placeHolder = "{{" + entry.getKey() + "}}";
                byte[] fileBytes = entry.getValue();

                Path subPath = null;
                try {
                    // 2. 为每个子文档创建临时文件
                    subPath = Files.createTempFile("sub_doc", ".docx");
                    Files.write(subPath, fileBytes);

                    // 假设的合并方法，它接收文件和输出流
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    // 注意：mergeDocxAtPlaceholder 需要能够处理文件输入
                    mergeDocxAtPlaceholder(templatePath.toFile(), subPath.toFile(), placeHolder, out);

                    // 3. 用合并后的结果覆盖主模板临时文件
                    Files.write(templatePath, out.toByteArray());

                } finally {
                    // 4. 清理子文档的临时文件（在每次循环结束时）
                    if (subPath != null) {
                        try {
                            Files.deleteIfExists(subPath);
                            log.debug("已清理临时子文件: {}", subPath);
                        } catch (Exception e) {
                            log.error("清理临时子文件失败: {}", subPath, e);
                        }
                    }
                }
            }
            // 5. 读取最终合并后的文件内容
            return Files.readAllBytes(templatePath);
        } finally {
            // 6. 清理主模板的临时文件（在方法退出前）
            if (templatePath != null) {
                try {
                    Files.deleteIfExists(templatePath);
                    log.debug("已清理临时模板文件: {}", templatePath);
                } catch (Exception e) {
                    log.error("清理临时模板文件失败: {}", templatePath, e);
                }
            }
        }
    }

    /**
     * 将子文档合并到主模板的占位符位置（保留格式版本）
     *
     * @param templateFile 主模板文件
     * @param subDocFile   要插入的子文档文件
     * @param placeholder  模板中的占位符，例如 "${产品名称}"
     * @param outputFile   合并后的输出文件
     */
    public void mergeDocxAtPlaceholder(File templateFile, File subDocFile, String placeholder, ByteArrayOutputStream outputFile) throws Exception {
        log.info("开始文档合并，模板: {}, 子文档: {}, 占位符: {}", templateFile.getName(), subDocFile.getName(), placeholder);

        // 0. 验证占位符格式
        if (!isValidPlaceholder(placeholder)) {
            throw new IllegalArgumentException("无效的占位符格式: " + placeholder + "，期望格式：{{变量名}} 或 ${变量名}");
        }

        // 1. 加载主模板
        WordprocessingMLPackage templatePackage = WordprocessingMLPackage.load(templateFile);
        MainDocumentPart mainDocumentPart = templatePackage.getMainDocumentPart();

        // 2. 清理和合并分散的文本块
        org.docx4j.model.datastorage.migration.VariablePrepare.prepare(templatePackage);

        // 3. 查找包含占位符的段落
        List<P> placeholderParagraphs = new ArrayList<>();
        findPlaceholderParagraphs(mainDocumentPart.getContent(), placeholder, placeholderParagraphs);

        if (placeholderParagraphs.isEmpty()) {
            log.warn("未找到占位符 '{}' 在模板中", placeholder);
            // 直接复制原文件
            byte[] bytes = Files.readAllBytes(templateFile.toPath());
            outputFile.write(bytes);
            return;
        }

        log.info("找到 {} 个包含占位符 '{}' 的段落", placeholderParagraphs.size(), placeholder);

        // 4. 对每个包含占位符的段落进行处理
        // 从后往前处理，避免AltChunk替换时索引变化的问题
        for (int i = placeholderParagraphs.size() - 1; i >= 0; i--) {
            P paragraph = placeholderParagraphs.get(i);

            // 再次验证段落是否仍然包含占位符（防止重复处理）
            String paragraphText = extractParagraphText(paragraph);
            if (!containsExactPlaceholder(paragraphText, placeholder)) {
                log.debug("段落已被处理或不再包含占位符，跳过: {}", paragraphText.trim());
                continue;
            }

            log.info("处理第 {} 个占位符段落: '{}'", (placeholderParagraphs.size() - i), paragraphText.trim());

            // 检查段落是否只包含占位符（独占一行）
            if (paragraphText.trim().equals(placeholder)) {
                // 独占一行，使用AltChunk替换整个段落
                replaceWithAltChunk(mainDocumentPart, paragraph, subDocFile, i);
                log.info("使用AltChunk替换独占段落: {}", placeholder);
            } else {
                // 段落中包含其他内容，只替换占位符部分
                String subDocText = extractTextFromDocx(subDocFile);
                if (subDocText != null && !subDocText.trim().isEmpty()) {
                    replaceTextInParagraph(paragraph, placeholder, subDocText);
                    log.info("在段落中替换占位符文本: {} -> {}", placeholder, subDocText.substring(0, Math.min(50, subDocText.length())) + "...");
                } else {
                    log.warn("子文档 {} 内容为空，跳过替换", subDocFile.getName());
                }
            }
        }

        // 5. 保存最终的文档
        Docx4J.save(templatePackage, outputFile, Docx4J.FLAG_SAVE_ZIP_FILE);
        log.info("文件合并完成");
    }

    /**
     * 在段落中替换文本（改进版本，保留格式）
     */
    private void replaceTextInParagraph(P paragraph, String placeholder, String replacement) {
        try {
            // 获取段落的完整文本（不包含批注）
            String fullText = extractTextWithoutComments(paragraph);

            if (fullText.contains(placeholder)) {
                log.debug("在段落中找到占位符: '{}', 段落文本: '{}'", placeholder, fullText);

                // 在Run级别进行替换，保留格式
                boolean replaced = replaceTextInRuns(paragraph.getContent(), placeholder, replacement);

                if (replaced) {
                    log.debug("段落文本替换成功: {} -> {}", placeholder, replacement);
                } else {
                    log.warn("段落文本替换失败，未找到可替换的Run");
                }
            }
        } catch (Exception e) {
            log.warn("段落文本替换失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 在Run级别替换文本，保留格式
     */
    private boolean replaceTextInRuns(List<Object> content, String placeholder, String replacement) {
        boolean hasReplacement = false;
        for (Object element : content) {
            if (element instanceof R) {
                R run = (R) element;
                if (replaceTextInRun(run, placeholder, replacement)) {
                    hasReplacement = true;
                }
            } else if (element instanceof JAXBElement) {
                Object value = ((JAXBElement<?>) element).getValue();
                if (value instanceof R) {
                    if (replaceTextInRun((R) value, placeholder, replacement)) {
                        hasReplacement = true;
                    }
                }
            }
        }
        return hasReplacement;
    }

    /**
     * 在单个Run中替换文本（改进版本）
     */
    private boolean replaceTextInRun(R run, String placeholder, String replacement) {
        List<Object> runContent = run.getContent();
        boolean hasReplacement = false;

        // 遍历Run中的所有内容
        for (int i = 0; i < runContent.size(); i++) {
            Object content = runContent.get(i);
            Text textElement = null;

            // 获取Text元素
            if (content instanceof Text) {
                textElement = (Text) content;
            } else if (content instanceof JAXBElement) {
                Object value = ((JAXBElement<?>) content).getValue();
                if (value instanceof Text) {
                    textElement = (Text) value;
                }
            }

            // 如果找到Text元素且包含占位符，进行替换
            if (textElement != null && textElement.getValue() != null) {
                String textValue = textElement.getValue();
                if (textValue.contains(placeholder)) {
                    String newValue = textValue.replace(placeholder, replacement);
                    textElement.setValue(newValue);
                    hasReplacement = true;
                    log.debug("在Run中替换文本: '{}' -> '{}'", textValue, newValue);
                }
            }
        }

        return hasReplacement;
    }

    /**
     * 提取段落文本，排除批注内容
     */
    private String extractTextWithoutComments(P paragraph) {
        StringBuilder text = new StringBuilder();
        boolean inComment = false;

        for (Object element : paragraph.getContent()) {
            if (element instanceof R) {
                R run = (R) element;
                for (Object runContent : run.getContent()) {
                    if (runContent instanceof CommentRangeStart) {
                        inComment = true;
                    } else if (runContent instanceof CommentRangeEnd) {
                        inComment = false;
                    } else if (runContent instanceof R.CommentReference) {
                        // 跳过批注引用
                        continue;
                    } else if (!inComment && runContent instanceof Text) {
                        text.append(((Text) runContent).getValue());
                    } else if (!inComment && runContent instanceof JAXBElement) {
                        Object value = ((JAXBElement<?>) runContent).getValue();
                        if (value instanceof Text) {
                            text.append(((Text) value).getValue());
                        }
                    }
                }
            }
        }

        return text.toString();
    }

    /**
     * 从Word文档中提取纯文本内容
     */
    private String extractTextFromDocx(File docxFile) {
        try {
            WordprocessingMLPackage wordPackage = WordprocessingMLPackage.load(docxFile);
            MainDocumentPart mainDocumentPart = wordPackage.getMainDocumentPart();

            StringBuilder text = new StringBuilder();
            extractTextFromContent(mainDocumentPart.getContent(), text);

            return text.toString().trim();
        } catch (Exception e) {
            log.error("提取文档文本失败: {}", docxFile.getName(), e);
            return null;
        }
    }

    /**
     * 从内容中递归提取文本
     */
    private void extractTextFromContent(List<Object> content, StringBuilder text) {
        for (Object element : content) {
            if (element instanceof P) {
                P paragraph = (P) element;
                for (Object pContent : paragraph.getContent()) {
                    if (pContent instanceof R) {
                        R run = (R) pContent;
                        for (Object runContent : run.getContent()) {
                            if (runContent instanceof Text) {
                                text.append(((Text) runContent).getValue());
                            } else if (runContent instanceof JAXBElement) {
                                Object value = ((JAXBElement<?>) runContent).getValue();
                                if (value instanceof Text) {
                                    text.append(((Text) value).getValue());
                                }
                            }
                        }
                    }
                }
                text.append("\n"); // 段落换行
            } else if (element instanceof JAXBElement) {
                Object value = ((JAXBElement<?>) element).getValue();
                if (value instanceof ContentAccessor) {
                    extractTextFromContent(((ContentAccessor) value).getContent(), text);
                }
            } else if (element instanceof ContentAccessor) {
                extractTextFromContent(((ContentAccessor) element).getContent(), text);
            }
        }
    }

    /**
     * 使用AltChunk替换整个段落
     */
    private void replaceWithAltChunk(MainDocumentPart mainDocumentPart, P paragraph, File subDocFile, int index) throws Exception {
        try (InputStream subDocInputStream = new FileInputStream(subDocFile)) {
            // 创建唯一的AltChunk部分
            AlternativeFormatInputPart afiPart = new AlternativeFormatInputPart(
                new org.docx4j.openpackaging.parts.PartName("/chunk_" + index + "_" + System.currentTimeMillis() + ".docx")
            );

            afiPart.setBinaryData(subDocInputStream);

            // 将AltChunk Part添加到主文档
            Relationship relationship = mainDocumentPart.addTargetPart(afiPart);

            // 创建CTAltChunk对象
            CTAltChunk altChunk = Context.getWmlObjectFactory().createCTAltChunk();
            altChunk.setId(relationship.getId());

            // 替换段落
            ContentAccessor parent = (ContentAccessor) paragraph.getParent();
            List<Object> parentContent = parent.getContent();
            int paragraphIndex = parentContent.indexOf(paragraph);
            if (paragraphIndex >= 0) {
                parentContent.remove(paragraphIndex);
                parentContent.add(paragraphIndex, altChunk);
            }
        }
    }

    /**
     * 验证占位符格式是否正确
     * 期望格式：{{变量名}} 或 ${变量名}
     */
    private boolean isValidPlaceholder(String placeholder) {
        if (placeholder == null || placeholder.length() < 4) {
            return false;
        }

        // 检查是否是 {{...}} 或 ${...} 格式
        return (placeholder.startsWith("{{") && placeholder.endsWith("}}")) ||
            (placeholder.startsWith("${") && placeholder.endsWith("}"));
    }

    /**
     * 查找包含占位符的段落（精确匹配版本）
     */
    private void findPlaceholderParagraphs(List<Object> content, String placeholder, List<P> results) {
        for (Object element : content) {
            if (element instanceof P) {
                P paragraph = (P) element;
                String text = extractParagraphText(paragraph);
                // 使用精确匹配，避免误匹配不完整的占位符
                if (containsExactPlaceholder(text, placeholder)) {
                    results.add(paragraph);
                    log.debug("找到包含占位符 '{}' 的段落: '{}'", placeholder, text.trim());
                }
            } else if (element instanceof JAXBElement) {
                Object value = ((JAXBElement<?>) element).getValue();
                if (value instanceof ContentAccessor) {
                    findPlaceholderParagraphs(((ContentAccessor) value).getContent(), placeholder, results);
                }
            } else if (element instanceof ContentAccessor) {
                findPlaceholderParagraphs(((ContentAccessor) element).getContent(), placeholder, results);
            }
        }
    }

    /**
     * 提取段落的纯文本内容
     */
    private String extractParagraphText(P paragraph) {
        try {
            StringWriter writer = new StringWriter();
            TextUtils.extractText(paragraph, writer);
            return writer.toString();
        } catch (Exception e) {
            log.warn("提取段落文本失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 检查文本是否包含精确的占位符
     * 避免误匹配部分占位符（例如：{{估值对象}} vs 估值对象）
     */
    private boolean containsExactPlaceholder(String text, String placeholder) {
        if (text == null || placeholder == null) {
            return false;
        }

        // 验证占位符格式
        if (!isValidPlaceholder(placeholder)) {
            log.warn("无效的占位符格式: {}", placeholder);
            return false;
        }

        // 直接查找完整的占位符
        return text.contains(placeholder);
    }

    /**
     * 验证文档是否为有效的docx格式
     */
    private boolean isValidDocxFormat(byte[] docxBytes) {
        if (docxBytes == null || docxBytes.length < 30) {
            return false;
        }

        try {
            // 检查ZIP文件头
            if (docxBytes[0] != 'P' || docxBytes[1] != 'K') {
                return false;
            }

            // 尝试创建WordprocessingMLPackage来验证
            WordprocessingMLPackage.load(new ByteArrayInputStream(docxBytes));
            return true;
        } catch (Exception e) {
            log.debug("文档格式验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 向后兼容：纯文本替换方法
     */
    public byte[] replaceTextInDocxBytes(byte[] docxBytes, Map<String, String> replacements) throws Exception {
        return poiTlTextReplace(docxBytes, replacements);
    }

    /**
     * 向后兼容：纯文件替换方法
     */
    public byte[] mergeDocxAtPlaceholderBytes(byte[] templateBytes, byte[] subDocBytes, String placeholder) throws Exception {
        Map<String, byte[]> fileReplacements = new HashMap<>();
        fileReplacements.put(placeholder, subDocBytes);
        return docx4jFileReplace(templateBytes, fileReplacements);
    }

    /**
     * 完整的混合替换方法 (推荐使用)
     */
    public byte[] mixedReplace(byte[] templateBytes, Map<String, String> textReplacements,
                              Map<String, byte[]> docReplacements) throws Exception {
        return highQualityMixedReplace(templateBytes, textReplacements, docReplacements);
    }

    // === 文件版本的方法 ===

    /**
     * 文件版本：高格式要求的混合替换
     */
    public void highQualityMixedReplace(File templateFile, Map<String, String> textReplacements,
                                       Map<String, byte[]> fileReplacements, File outputFile) throws Exception {

        // 读取模板文件
        byte[] templateBytes;
        try (FileInputStream fis = new FileInputStream(templateFile);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[8192];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                baos.write(buffer, 0, len);
            }
            templateBytes = baos.toByteArray();
        }

        // 执行混合替换
        byte[] result = highQualityMixedReplace(templateBytes, textReplacements, fileReplacements);

        // 写入输出文件
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(result);
        }

        log.info("文件版本混合替换完成: {}", outputFile.getName());
    }
}
