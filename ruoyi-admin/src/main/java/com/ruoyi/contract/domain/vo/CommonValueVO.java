package com.ruoyi.contract.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公共值VO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Data
public class CommonValueVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 归属产品类型
     */
    private String productType;

    /**
     * 归属模板分类
     */
    private String templateCategory;

    /**
     * 公共值名称
     */
    private String name;

    /**
     * 公共值内容 (对于简单文本) 或 文件路径 (对于文件)
     */
    private String valueContent;

    /**
     * 公共值类型: FILE (文件路径) 或 TEXT (纯文本)
     */
    private String valueType;

    /**
     * HTML内容，用于预览显示
     */
    private String htmlContent;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
}
