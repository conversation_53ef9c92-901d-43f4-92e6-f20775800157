package com.ruoyi.contract.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合同管理主数据实体类 (合同台账)
 * 对应表：con_master_contract
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("con_master_contract")
public class MasterContract implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花算法生成的ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联的生成合同ID
     */
    @TableField("generated_contract_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long generatedContractId;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 合同名称
     */
    @TableField("contract_name")
    private String contractName;

    /**
     * 产品全称
     */
    @TableField("product_full_name")
    private String productFullName;

    /**
     * 产品简称
     */
    @TableField("product_short_name")
    private String productShortName;

    /**
     * 英文名称
     */
    @TableField("product_english_name")
    private String productEnglishName;

    /**
     * 英文简称
     */
    @TableField("product_english_short_name")
    private String productEnglishShortName;

    /**
     * 产品代码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 组合代码
     */
    @TableField("portfolio_code")
    private String portfolioCode;

    /**
     * 资产代码
     */
    @TableField("asset_code")
    private String assetCode;

    /**
     * 托管机构简称
     */
    @TableField("custodian_short_name")
    private String custodianShortName;

    /**
     * 托管机构名称
     */
    @TableField("custodian_name")
    private String custodianName;

    /**
     * 托管机构代码
     */
    @TableField("custodian_code")
    private String custodianCode;

    /**
     * 托管账户名称
     */
    @TableField("custodian_account_name")
    private String custodianAccountName;

    /**
     * 托管账户户号
     */
    @TableField("custodian_account_number")
    private String custodianAccountNumber;

    /**
     * 开户行
     */
    @TableField("custodian_bank_name")
    private String custodianBankName;

    /**
     * 大额支付号
     */
    @TableField("large_payment_number")
    private String largePaymentNumber;

    /**
     * 合同编号
     */
    @TableField("contract_number")
    private String contractNumber;

    /**
     * 合同分类
     */
    @TableField("contract_category")
    private String contractCategory;

    /**
     * 合同状态
     */
    @TableField("contract_status")
    private String contractStatus;

    /**
     * 合同签署日期
     */
    @TableField("signing_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate signingDate;

    /**
     * 合同生效日期
     */
    @TableField("effective_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate effectiveDate;

    /**
     * 合同到期日期
     */
    @TableField("expiry_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expiryDate;

    /**
     * 合同续签日期
     */
    @TableField("renewal_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate renewalDate;

    /**
     * 合同用印方式
     */
    @TableField("seal_method")
    private String sealMethod;

    /**
     * 用印状态
     */
    @TableField("seal_status")
    private String sealStatus;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
}
