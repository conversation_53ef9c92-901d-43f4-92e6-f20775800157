package com.ruoyi.contract.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 参数项DTO
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
public class ParamItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数名
     */
    private String key;

    /**
     * 文件路径或文本内容
     */
    private String path;

    /**
     * HTML内容，用于预览显示
     */
    private String html;

    /**
     * 参数值类型: FILE (文件路径) 或 TEXT (纯文本)
     */
    private String valueType;
}
