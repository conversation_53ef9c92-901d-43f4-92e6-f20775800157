package com.ruoyi.contract.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 生成合同实体类 (合同生命周期的起点)
 * 对应表：con_generated_contract
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("con_generated_contract")
public class GeneratedContract implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花算法生成的ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 合同名称 (用户可修改)
     */
    @TableField("contract_name")
    private String contractName;

    /**
     * 产品代码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 产品类型
     */
    @TableField("product_type")
    private String productType;

    /**
     * 处理状态 (审核中, 审核通过)
     */
    @TableField("status")
    private String status;

    /**
     * 投资经理
     */
    @TableField("investment_manager")
    private String investmentManager;

    /**
     * 产品经理
     */
    @TableField("product_manager")
    private String productManager;

    /**
     * 创建人ID
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人姓名
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 使用的模板版本ID
     */
    @TableField("template_version_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateVersionId;

    /**
     * 使用的参数集ID
     */
    @TableField("parameter_set_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parameterSetId;

    /**
     * 指向当前最新合同文件版本的ID
     */
    @TableField("current_doc_version_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long currentDocVersionId;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 状态常量
     */
    public static final String STATUS_PENDING = "审核中";
    public static final String STATUS_APPROVED = "审核通过";
}
