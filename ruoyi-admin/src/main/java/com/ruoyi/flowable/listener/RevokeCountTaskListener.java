package com.ruoyi.flowable.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.workflow.domain.WfFormHistory;
import com.ruoyi.workflow.mapper.WfFormHistoryMapper;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 撤回次数计算任务监听器
 * 用于计算当前流程实例的撤回次数，并将结果设置为流程变量
 *
 * <AUTHOR>
 */
@Slf4j
public class RevokeCountTaskListener implements TaskListener {

    private RuntimeService runtimeService;
    private WfFormHistoryMapper wfFormHistoryMapper;

    /**
     * 撤回次数变量名字段（通过流程设计器配置）
     * 默认值：revokeCount
     */
    @Setter
    private org.flowable.common.engine.api.delegate.Expression variableName;

    /**
     * 是否包含当前操作字段（通过流程设计器配置）
     * 默认值：false（不包含当前操作）
     */
    @Setter
    private org.flowable.common.engine.api.delegate.Expression includeCurrent;

    /**
     * 操作类型过滤字段（通过流程设计器配置）
     * 可选值：ALL（所有操作）、RETURN（仅退回）、REVOKE（仅撤回）等
     * 默认值：ALL
     */
    @Setter
    private org.flowable.common.engine.api.delegate.Expression operationTypeFilter;

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("撤回次数计算监听器触发，任务: {}", delegateTask.getName());

        try {
            // 只在任务创建时执行
            if (!EVENTNAME_CREATE.equals(delegateTask.getEventName())) {
                log.debug("非任务创建事件，跳过撤回次数计算");
                return;
            }

            // 计算撤回次数
            int revokeCount = calculateRevokeCount(delegateTask);

            // 获取变量名
            String varName = getFieldValue(variableName, delegateTask);
            if (varName == null || varName.trim().isEmpty()) {
                varName = "revokeCount"; // 默认变量名
            }

            // 设置流程变量
            getRuntimeService().setVariable(delegateTask.getProcessInstanceId(), varName, revokeCount);

            log.info("撤回次数计算完成，任务: {}, 变量名: {}, 撤回次数: {}",
                delegateTask.getName(), varName, revokeCount);

        } catch (Exception e) {
            log.error("计算撤回次数时发生异常，任务: {}", delegateTask.getName(), e);
            // 不抛出异常，避免影响正常流程
        }
    }

    /**
     * 计算当前流程实例的撤回次数
     */
    private int calculateRevokeCount(DelegateTask delegateTask) {
        try {
            String processInstanceId = delegateTask.getProcessInstanceId();

            // 构建查询条件
            LambdaQueryWrapper<WfFormHistory> queryWrapper = new LambdaQueryWrapper<WfFormHistory>()
                .eq(WfFormHistory::getProcessInstanceId, processInstanceId);

            // 根据操作类型过滤
            String operationFilter = getFieldValue(operationTypeFilter, delegateTask);
            if (operationFilter != null && !operationFilter.trim().isEmpty() && !"ALL".equalsIgnoreCase(operationFilter)) {
                queryWrapper.eq(WfFormHistory::getOperationType, operationFilter.toUpperCase());
            }

            // 是否包含当前操作
            String includeCurrentValue = getFieldValue(includeCurrent, delegateTask);
            boolean shouldIncludeCurrent = "true".equalsIgnoreCase(includeCurrentValue);

            long count = getWfFormHistoryMapper().selectCount(queryWrapper);

            // 如果包含当前操作，则加1
            if (shouldIncludeCurrent) {
                count++;
            }

            log.debug("流程实例 {} 的撤回次数: {}, 操作类型过滤: {}, 包含当前操作: {}",
                processInstanceId, count, operationFilter, shouldIncludeCurrent);

            return Math.toIntExact(count);

        } catch (Exception e) {
            log.warn("计算撤回次数失败，使用默认值0", e);
            return 0;
        }
    }

    /**
     * 获取字段值
     */
    private String getFieldValue(org.flowable.common.engine.api.delegate.Expression expression, DelegateTask delegateTask) {
        if (expression == null) {
            return null;
        }
        try {
            Object value = expression.getValue(delegateTask);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("获取字段值失败", e);
            return null;
        }
    }

    /**
     * 获取RuntimeService
     */
    private RuntimeService getRuntimeService() {
        if (runtimeService == null) {
            try {
                runtimeService = SpringUtils.getBean(RuntimeService.class);
            } catch (Exception e) {
                log.error("无法获取RuntimeService", e);
                throw new RuntimeException("无法获取RuntimeService", e);
            }
        }
        return runtimeService;
    }

    /**
     * 获取WfFormHistoryMapper
     */
    private WfFormHistoryMapper getWfFormHistoryMapper() {
        if (wfFormHistoryMapper == null) {
            try {
                wfFormHistoryMapper = SpringUtils.getBean(WfFormHistoryMapper.class);
            } catch (Exception e) {
                log.error("无法获取WfFormHistoryMapper", e);
                throw new RuntimeException("无法获取WfFormHistoryMapper", e);
            }
        }
        return wfFormHistoryMapper;
    }
}
