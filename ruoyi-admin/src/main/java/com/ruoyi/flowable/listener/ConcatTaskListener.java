package com.ruoyi.flowable.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.contract.domain.ParameterSet;
import com.ruoyi.contract.domain.dto.ParamItemDTO;
import com.ruoyi.contract.domain.dto.ParamSetDTO;
import com.ruoyi.contract.service.ParamService;
import com.ruoyi.contract.utils.Docx4jConverter;
import com.ruoyi.contract.utils.DocxReportGenerator;
import com.ruoyi.contract.utils.OssService;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.el.FixedValue;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户任务监听器
 *
 * <AUTHOR>
 * @since 2023/5/13
 */
@Slf4j
@Data
@Component("concatTaskListener")
public class ConcatTaskListener implements TaskListener {

    /**
     * 合同模板ID
     */
    private FixedValue concatTemplateId;

    private FixedValue concatSetCode;

    private ParamService paramService;

    @Autowired
    public void setParamService(ParamService paramService) {
        this.paramService = paramService;
    }

    private ParamService getParamService() {
        if (paramService == null) {
            paramService = SpringUtils.getBean(ParamService.class);
        }
        return paramService;
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        Objects.requireNonNull(delegateTask, "委托任务不能为空");
        log.info("执行任务监听器...");
        Map<String, Object> variables = delegateTask.getVariables();
        log.info("传递的参数: {}", variables);
        Map<String, Object> filteredMap = variables.entrySet()
                .stream()
                .filter(entry -> {
                    String key = entry.getKey();
                    return key != null && !key.isEmpty() && !key.matches("^[a-zA-Z].*");
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        log.info("过滤的参数: {}", variables);
        //模板列表创建参数 选择模板
        String templateId = concatTemplateId.getExpressionText();
        String setCode = null;
        if (concatSetCode != null) {
            setCode = concatSetCode.getExpressionText();
        }
        paramService = getParamService();
        if (setCode == null) {
            setCode = IdWorker.getIdStr();
        }
        Objects.requireNonNull(templateId, "模板参数不得为空");
        ParamSetDTO paramSetDTO = new ParamSetDTO();
        paramSetDTO.setTemplateId(Long.valueOf(templateId));
        paramSetDTO.setSetCode(setCode);
        paramSetDTO.setParams(convertToParamItem(filteredMap));
        getParamService().saveOrUpdateParamSet(paramSetDTO);
    }

    private @NonNull List<ParamItemDTO> convertToParamItem(Map<String, Object> variables) {
        Objects.requireNonNull(variables, "变量列表不能为空");
        List<ParamItemDTO> result = new ArrayList<>();
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value == null) {
                continue;
            }
            if ("估值表".equals(key)) {
                ParamItemDTO gzb = composeGZB(value);
                result.add(gzb);
            }else{
                ParamItemDTO item = new ParamItemDTO();
                item.setKey(key);
                item.setPath(value.toString());
                item.setHtml(value.toString());
                item.setValueType("TEXT");
                result.add(item);
            }
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    private @NonNull ParamItemDTO composeGZB(Object value) {
        Objects.requireNonNull(value, "估值表不能为空");
        List<List<String>> outerList = (List<List<String>>) value;
        List<String> resultList = outerList.stream() // 转换为 Stream<List<String>>
                .filter(innerList -> innerList != null && !innerList.isEmpty()) // 过滤掉空列表
                .map(innerList -> innerList.get(innerList.size() - 1)) // 映射为每个列表的最后一个元素
                .collect(Collectors.toCollection(ArrayList::new));
        byte[] bytes = DocxReportGenerator.generateReport(resultList);
        OssService ossService = SpringUtils.getBean(OssService.class);
        String filePath = ossService.uploadFile(
                new ByteArrayInputStream(bytes),
                "估值表.docx",
                "param"
        );
        ParamItemDTO paramItemDTO = new ParamItemDTO();
        paramItemDTO.setKey("估值表");
        paramItemDTO.setPath(filePath);
        paramItemDTO.setHtml(Docx4jConverter.convertToHtml(bytes));
        paramItemDTO.setValueType("FILE");
        return paramItemDTO;
    }

}
