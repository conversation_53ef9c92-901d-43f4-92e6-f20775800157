package com.ruoyi.flowable.listener;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.IdentityService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import com.ruoyi.common.utils.spring.SpringUtils;

import java.util.Map;

/**
 * 基于BPMN字段配置的自动审批任务监听器
 * 通过在BPMN流程设计器中配置字段来控制自动审批
 * <p>
 * 支持的字段：
 * - approvalType: 审批类型 (unconditional/conditional/disabled)
 * - condition: 条件表达式（条件审批时使用）
 * - comment: 审批意见
 * - approvalUser: 审批用户
 *
 * <AUTHOR>
 */
@Slf4j
public class FieldAutoApprovalTaskListener implements TaskListener {

    private TaskService taskService;
    private IdentityService identityService;

    // BPMN字段配置（通过流程设计器配置）
    @Setter
    private Expression approvalType;    // 审批类型：unconditional/conditional/disabled
    @Setter
    private Expression condition;       // 条件表达式（条件审批时使用）
    @Setter
    private Expression comment;         // 审批意见
    @Setter
    private Expression approvalUser;    // 审批用户

    private final ExpressionParser expressionParser = new SpelExpressionParser();

    @Override
    public void notify(DelegateTask delegateTask) {
        log.debug("字段自动审批监听器触发，任务: {}", delegateTask.getName());

        try {
            // 只在任务创建时执行
            if (!EVENTNAME_CREATE.equals(delegateTask.getEventName())) {
                return;
            }

            // 获取审批类型
            String approvalTypeValue = getFieldValue(approvalType, delegateTask);
            if (approvalTypeValue == null || "disabled".equalsIgnoreCase(approvalTypeValue)) {
                log.debug("任务 {} 未配置自动审批或已禁用", delegateTask.getName());
                return;
            }

            log.info("检测到自动审批任务: {}, 类型: {}", delegateTask.getName(), approvalTypeValue);

            boolean shouldApprove = false;

            switch (approvalTypeValue.toLowerCase()) {
                case "unconditional":
                    shouldApprove = true;
                    log.info("无条件自动审批，任务: {}", delegateTask.getName());
                    break;

                case "conditional":
                    shouldApprove = evaluateCondition(delegateTask);
                    log.info("条件审批结果: {}, 任务: {}", shouldApprove, delegateTask.getName());
                    break;

                default:
                    log.warn("未知的自动审批类型: {}, 任务: {}", approvalTypeValue, delegateTask.getName());
                    return;
            }

            // 执行自动审批
            if (shouldApprove) {
                executeAutoApproval(delegateTask);
            }

        } catch (Exception e) {
            log.error("自动审批执行异常，任务: {}", delegateTask.getName(), e);
            // 不抛出异常，避免影响正常流程
        }
    }

    /**
     * 评估条件表达式
     */
    private boolean evaluateCondition(DelegateTask delegateTask) {
        String conditionExpr = getFieldValue(condition, delegateTask);

        if (conditionExpr == null || conditionExpr.trim().isEmpty()) {
            log.warn("条件审批但未配置条件表达式，任务: {}", delegateTask.getName());
            return false;
        }

        try {
            log.debug("执行条件表达式: {}, 任务: {}", conditionExpr, delegateTask.getName());

            // 获取流程变量
            Map<String, Object> variables = delegateTask.getVariables();

            // 创建表达式上下文
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariables(variables);

            // 解析并执行表达式
            org.springframework.expression.Expression expression = expressionParser.parseExpression(conditionExpr);
            Boolean result = expression.getValue(context, Boolean.class);

            log.debug("条件表达式结果: {}, 表达式: {}", result, conditionExpr);
            return Boolean.TRUE.equals(result);

        } catch (Exception e) {
            log.error("条件表达式执行失败，任务: {}, 表达式: {}", delegateTask.getName(), conditionExpr, e);
            return false;
        }
    }

    /**
     * 执行自动审批
     */
    private void executeAutoApproval(DelegateTask delegateTask) {
        try {
            // 获取服务实例
            TaskService taskService = getTaskService();
            IdentityService identityService = getIdentityService();

            // 设置审批用户
            String user = getFieldValue(approvalUser, delegateTask);
            if (user == null || user.trim().isEmpty()) {
                user = "system";
            }
            identityService.setAuthenticatedUserId(user);

            // 添加审批意见
            String commentText = getFieldValue(comment, delegateTask);
            if (commentText == null || commentText.trim().isEmpty()) {
                commentText = "系统自动审批通过";
            }

            taskService.addComment(
                delegateTask.getId(),
                delegateTask.getProcessInstanceId(),
                "normal",
                commentText
            );

            // 完成任务
            taskService.complete(delegateTask.getId());

            log.info("自动审批完成，任务: {}, 审批人: {}, 意见: {}",
                delegateTask.getName(), user, commentText);

        } catch (Exception e) {
            log.error("执行自动审批失败，任务: {}", delegateTask.getName(), e);
            throw e;
        }
    }

    /**
     * 获取TaskService
     */
    private TaskService getTaskService() {
        if (taskService == null) {
            try {
                taskService = SpringUtils.getBean(TaskService.class);
            } catch (Exception e) {
                log.error("无法获取TaskService", e);
                throw new RuntimeException("无法获取TaskService", e);
            }
        }
        return taskService;
    }

    /**
     * 获取IdentityService
     */
    private IdentityService getIdentityService() {
        if (identityService == null) {
            try {
                identityService = SpringUtils.getBean(IdentityService.class);
            } catch (Exception e) {
                log.error("无法获取IdentityService", e);
                throw new RuntimeException("无法获取IdentityService", e);
            }
        }
        return identityService;
    }

    /**
     * 获取字段值
     */
    private String getFieldValue(Expression expression, DelegateTask delegateTask) {
        if (expression == null) {
            return null;
        }
        try {
            Object value = expression.getValue(delegateTask);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.error("获取字段值失败", e);
            return null;
        }
    }

}
