package com.ruoyi.flowable.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.JsonUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.flowable.utils.TaskUtils;
import com.ruoyi.workflow.domain.WfFormHistory;
import com.ruoyi.workflow.mapper.WfFormHistoryMapper;
// import com.ruoyi.flowable.utils.TaskUtils; // 使用 LoginHelper 替代
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 表单历史记录任务监听器
 * 用于在任务触发时保存表单历史数据
 *
 * <AUTHOR>
 */
@Slf4j
public class FormHistoryTaskListener implements TaskListener {

    private TaskService taskService;
    private WfFormHistoryMapper wfFormHistoryMapper;

    /**
     * 操作类型字段（通过流程设计器配置）
     * 可选值：TRIGGER（触发器保存）、RETURN（退回）、REVOKE（撤回）等
     */
    @Setter
    private org.flowable.common.engine.api.delegate.Expression operationType;

    /**
     * 备注信息字段（通过流程设计器配置）
     */
    @Setter
    private org.flowable.common.engine.api.delegate.Expression remark;

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("表单历史记录监听器触发，任务: {}", delegateTask.getName());

        try {
            // 只在任务完成时执行
            if (!EVENTNAME_COMPLETE.equals(delegateTask.getEventName())) {
                log.debug("非任务完成事件，跳过表单历史记录保存");
                return;
            }

            // 保存表单历史数据
            saveFormHistoryData(delegateTask);

        } catch (Exception e) {
            log.error("保存表单历史数据时发生异常，任务: {}", delegateTask.getName(), e);
            // 不抛出异常，避免影响正常流程
        }
    }

    /**
     * 保存表单历史数据
     */
    private void saveFormHistoryData(DelegateTask delegateTask) {
        try {
            // 获取任务的表单数据
            Map<String, Object> taskVariables = getTaskService().getVariables(delegateTask.getId());
            Map<String, Object> localVariables = getTaskService().getVariablesLocal(delegateTask.getId());

            // 合并全局变量和本地变量
            Map<String, Object> allVariables = new HashMap<>(taskVariables);
            allVariables.putAll(localVariables);

            // 如果没有表单数据，跳过保存
            if (allVariables.isEmpty()) {
                log.debug("任务 {} 没有表单数据，跳过历史记录保存", delegateTask.getName());
                return;
            }

            // 计算撤回次数
            int revokeCount = calculateRevokeCount(delegateTask.getProcessInstanceId()) + 1;

            // 获取操作类型
            String operationTypeValue = getFieldValue(operationType, delegateTask);
            if (operationTypeValue == null || operationTypeValue.trim().isEmpty()) {
                operationTypeValue = "TRIGGER"; // 默认为触发器保存
            }

            // 获取备注信息
            String remarkValue = getFieldValue(remark, delegateTask);
            if (remarkValue == null || remarkValue.trim().isEmpty()) {
                remarkValue = "任务触发器自动保存表单历史数据";
            }

            // 保存到数据库
            saveFormHistoryToDatabase(
                delegateTask.getProcessInstanceId(),
                delegateTask,
                allVariables,
                operationTypeValue,
                revokeCount,
                remarkValue
            );

            log.info("成功保存表单历史数据，任务: {} - {}", delegateTask.getName(), delegateTask.getId());

        } catch (Exception e) {
            log.error("保存表单历史数据失败，任务: {}", delegateTask.getName(), e);
            throw e;
        }
    }

    /**
     * 计算当前流程实例的撤回次数
     */
    private int calculateRevokeCount(String processInstanceId) {
        try {
            return Math.toIntExact(getWfFormHistoryMapper().selectCount(
                new LambdaQueryWrapper<WfFormHistory>()
                    .eq(WfFormHistory::getProcessInstanceId, processInstanceId)
            ));
        } catch (Exception e) {
            log.warn("计算撤回次数失败，使用默认值0", e);
            return 0;
        }
    }

    /**
     * 保存表单历史数据到数据库
     */
    private void saveFormHistoryToDatabase(String procInsId, DelegateTask task,
                                         Map<String, Object> formData, String operationType,
                                         int revokeCount, String remarkValue) {
        try {
            WfFormHistory formHistory = new WfFormHistory();
            formHistory.setProcessInstanceId(procInsId);
            formHistory.setTaskId(task.getId());
            formHistory.setTaskName(task.getName());
            formHistory.setOperationType(operationType);
            formHistory.setRevokeCount(revokeCount);
            formHistory.setFormData(JsonUtils.toJsonString(formData));
            formHistory.setOperatorId(String.valueOf(LoginHelper.getUserId()));
            formHistory.setOperatorName(LoginHelper.getUsername());
            formHistory.setOperationTime(new Date());
            formHistory.setRemark(remarkValue);
            formHistory.setCreateBy(String.valueOf(LoginHelper.getUserId()));
            formHistory.setCreateTime(new Date());

            getWfFormHistoryMapper().insert(formHistory);

            log.debug("表单历史数据已保存到数据库，ID: {}", formHistory.getId());

        } catch (Exception e) {
            log.error("保存表单历史数据到数据库失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取字段值
     */
    private String getFieldValue(org.flowable.common.engine.api.delegate.Expression expression, DelegateTask delegateTask) {
        if (expression == null) {
            return null;
        }
        try {
            Object value = expression.getValue(delegateTask);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("获取字段值失败", e);
            return null;
        }
    }

    /**
     * 获取TaskService
     */
    private TaskService getTaskService() {
        if (taskService == null) {
            try {
                taskService = SpringUtils.getBean(TaskService.class);
            } catch (Exception e) {
                log.error("无法获取TaskService", e);
                throw new RuntimeException("无法获取TaskService", e);
            }
        }
        return taskService;
    }

    /**
     * 获取WfFormHistoryMapper
     */
    private WfFormHistoryMapper getWfFormHistoryMapper() {
        if (wfFormHistoryMapper == null) {
            try {
                wfFormHistoryMapper = SpringUtils.getBean(WfFormHistoryMapper.class);
            } catch (Exception e) {
                log.error("无法获取WfFormHistoryMapper", e);
                throw new RuntimeException("无法获取WfFormHistoryMapper", e);
            }
        }
        return wfFormHistoryMapper;
    }
}
